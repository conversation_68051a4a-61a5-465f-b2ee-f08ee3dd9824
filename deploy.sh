#!/bin/bash

# LTDWJ Docker Deployment Script
# Run this script on your Debian 12 server

# Generate container name with today's date
CONTAINER_NAME="LTDWJ-$(date +%d%m%Y)"
OLD_CONTAINER_NAME="LTDWJ-30062025"

echo "🚀 Starting LTDWJ Docker Deployment..."
echo "📅 New container name: $CONTAINER_NAME"
echo "🗑️  Old container name: $OLD_CONTAINER_NAME"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Installing Docker..."

    # Update package index
    sudo apt-get update

    # Install prerequisites
    sudo apt-get install -y \
        ca-certificates \
        curl \
        gnupg \
        lsb-release

    # Add Docker's official GPG key
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/debian/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

    # Set up the repository
    echo \
      "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian \
      $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

    # Update package index again
    sudo apt-get update

    # Install Docker Engine
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

    # Add current user to docker group
    sudo usermod -aG docker $USER

    echo "✅ Docker installed successfully!"
    echo "⚠️  Please log out and log back in for group changes to take effect."
    echo "⚠️  Then run this script again."
    exit 0
fi

# Check if Docker Compose is available
if ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

echo "✅ Docker is installed and ready!"

# Stop and remove existing containers if they exist
echo "🛑 Stopping existing containers (if any)..."
echo "   Stopping old container: $OLD_CONTAINER_NAME"
docker stop $OLD_CONTAINER_NAME 2>/dev/null || true
docker rm $OLD_CONTAINER_NAME 2>/dev/null || true
echo "   Stopping new container: $CONTAINER_NAME (in case it exists)"
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# Create directories for static and media files
echo "📁 Creating static and media directories..."
mkdir -p ./staticfiles ./media

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create one using .env.template as reference."
    exit 1
fi

echo "✅ Found .env file"

# Load environment variables from .env file
source .env

# Override DEBUG to False for production deployment
export DEBUG=False

# Generate a new secret key for production if not set
if [ -z "$SECRET_KEY" ] || [ "$SECRET_KEY" = "your_secret_key_here" ]; then
    export SECRET_KEY=$(openssl rand -base64 32)
    echo "🔑 Generated new SECRET_KEY for production"
fi

# Use environment variables for deployment settings
DEPLOY_PORT=${CONTAINER_PORT:-8002}
DEPLOY_HOST=${HOST_IP:-**********}

# Build and run the container
echo "🔨 Building Docker image..."
docker build -t ltdwj-app .

echo "🚀 Starting container..."
docker run -d \
    --name $CONTAINER_NAME \
    --restart always \
    -p $DEPLOY_HOST:$DEPLOY_PORT:8000 \
    -v $(pwd)/staticfiles:/app/staticfiles \
    -v $(pwd)/media:/app/media \
    -e DEBUG=False \
    -e SECRET_KEY="$SECRET_KEY" \
    -e DB_NAME="$DB_NAME" \
    -e DB_USER="$DB_USER" \
    -e DB_PASSWORD="$DB_PASSWORD" \
    -e DB_HOST="$DB_HOST" \
    -e DB_PORT="$DB_PORT" \
    -e GOOGLE_OAUTH2_CLIENT_ID="$GOOGLE_OAUTH2_CLIENT_ID" \
    -e GOOGLE_OAUTH2_CLIENT_SECRET="$GOOGLE_OAUTH2_CLIENT_SECRET" \
    -e ALLOWED_HOSTS="$ALLOWED_HOSTS" \
    -e LOG_LEVEL="$LOG_LEVEL" \
    ltdwj-app

# Check if container is running
if docker ps | grep -q $CONTAINER_NAME; then
    echo "✅ Container $CONTAINER_NAME is running successfully!"
    echo "🌐 Application is available at: http://**********:8002"
    echo "📊 Container status:"
    docker ps | grep $CONTAINER_NAME
else
    echo "❌ Container failed to start. Checking logs..."
    docker logs $CONTAINER_NAME
    exit 1
fi

echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Useful commands:"
echo "  View logs:     docker logs $CONTAINER_NAME"
echo "  Stop:          docker stop $CONTAINER_NAME"
echo "  Start:         docker start $CONTAINER_NAME"
echo "  Restart:       docker restart $CONTAINER_NAME"
echo "  Remove:        docker stop $CONTAINER_NAME && docker rm $CONTAINER_NAME"
echo ""
echo "📊 Database: External PostgreSQL at **********:5432"
echo "🗄️  Database Name: JW"
echo "👤 Database User: postgres"
