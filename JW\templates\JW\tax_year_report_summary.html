{% extends 'JW/base.html' %}

{% block title %}Tax Year Summary Report {{ tax_year_display }}{% endblock %}

{% block content %}
<div class="container">
    {% if report_header %}
    <div class="text-center mb-4">
        {% if report_header.name %}<h3 class="mb-2">{{ report_header.name }}</h3>{% endif %}
        {% if report_header.address %}<p class="mb-2">{{ report_header.address }}</p>{% endif %}
        {% if report_header.phone %}<p class="mb-2">{{ report_header.phone }}</p>{% endif %}
        {% if report_header.email %}<p class="mb-2">{{ report_header.email }}</p>{% endif %}
        {% if report_header.website %}<p class="mb-2">{{ report_header.website }}</p>{% endif %}
        {% if report_header.business_number %}<p class="mb-2">Business Number: {{ report_header.business_number }}</p>{% endif %}
    </div>
    {% endif %}

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Financial Summary Report - Tax Year {{ tax_year_display }}</h2>
        <div>
            <a href="{% url 'tax_year_report_summary_pdf' %}?start_year={{ start_date.year }}" class="btn btn-success">
                <i class="fas fa-download"></i> Download PDF
            </a>
            <a href="{% url 'report_list' %}" class="btn btn-secondary ms-2">
                Back to Reports
            </a>
        </div>
    </div>

    <!-- Income Summary -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h4 class="mb-0">Income Summary</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <tbody>
                    <tr>
                        <th>Total Number of Lessons:</th>
                        <td class="text-end">{{ total_lessons }}</td>
                    </tr>
                    <tr>
                        <th>Total Hours:</th>
                        <td class="text-end">{{ total_hours|floatformat:1 }}</td>
                    </tr>
                    <tr>
                        <th>Average Price per Hour:</th>
                        <td class="text-end">£{{ avg_price_per_hour|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-success">
                        <th>Total Income:</th>
                        <td class="text-end">£{{ total_income|floatformat:2 }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Mileage Summary -->
    <div class="card mb-4">
        <div class="card-header bg-warning">
            <h4 class="mb-0">Mileage Summary</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <tbody>
                    <tr>
                        <th>Total Miles:</th>
                        <td class="text-end">{{ total_miles|floatformat:1 }}</td>
                    </tr>
                    <tr>
                        <th>Business Miles:</th>
                        <td class="text-end">{{ total_business_miles|floatformat:1 }} ({{ business_miles_percentage|floatformat:1 }}%)</td>
                    </tr>
                    <tr>
                        <th>Personal Miles:</th>
                        <td class="text-end">{{ total_personal_miles|floatformat:1 }} ({{ personal_miles_percentage|floatformat:1 }}%)</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Financial Summary -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">Financial Summary</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <tbody>
                    <tr class="table-success">
                        <th>Total Income</th>
                        <td class="text-end">£{{ total_income|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-danger">
                        <th>Total Business Expenses</th>
                        <td class="text-end">£{{ total_business_expenses|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-info">
                        <th>Total Fuel Expenses</th>
                        <td class="text-end">£{{ total_fuel_expenses|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-primary">
                        <th><strong>Net Income</strong></th>
                        <td class="text-end"><strong>£{{ net_income|floatformat:2 }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}