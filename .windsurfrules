# Windsurf Rules for Django Driving School Management System

[[calls]]
match = "when the user requests Django code examples, Django documentation, or Django-specific functionality"
tool = "context7"

[[calls]]
match = "when the user requests PostgreSQL queries, database schema information, or database operations"
tool = "postgres"

[[calls]]
match = "when the user requests file operations, directory listings, or file content"
tool = "filesystem"

[[calls]]
match = "when the user requests git operations, commit history, or version control information"
tool = "git"

[[calls]]
match = "when the user requests Python library documentation or Python-specific code examples"
tool = "context7"

[[calls]]
match = "when the user requests web search for current information, latest solutions, or troubleshooting"
tool = "brave-search"

[[calls]]
match = "when the user requests code examples, setup or configuration steps, or library/API documentation"
tool = "context7"
