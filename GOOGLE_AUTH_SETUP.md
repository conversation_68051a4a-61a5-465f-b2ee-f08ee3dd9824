# Google OAuth Setup Guide

This guide explains how to set up Google OAuth authentication for the Driving School Management System.

## Overview

The system has been configured to allow only two specific Google accounts to access the application:
- `<EMAIL>`
- `<EMAIL>`

Both users will be automatically logged in as the existing "John" user and will have access to all of <PERSON>'s data.

## Setup Steps

### 1. Install Dependencies

First, install the new requirements:

```bash
pip install -r requirements.txt
```

### 2. Google Cloud Console Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API and Google OAuth2 API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set the application type to "Web application"
6. Add authorized redirect URIs:
   - For production: `https://learntodrivewithjohn.co.uk/accounts/google/login/callback/`
   - For development: `http://localhost:8000/accounts/google/login/callback/`
7. Copy the Client ID and Client Secret

### 3. Environment Variables

Update your `.env` file with the Google OAuth credentials:

```bash
# Google OAuth Configuration
GOOGLE_OAUTH2_CLIENT_ID=your_actual_client_id_here
GOOGLE_OAUTH2_CLIENT_SECRET=your_actual_client_secret_here
```

### 4. Database Migration

Run the Django migrations to set up the new authentication tables:

```bash
python manage.py migrate
```

### 5. Setup Command

Run the setup command to configure Google OAuth:

```bash
# If the 'john' user doesn't exist, create it:
python manage.py setup_google_auth --create-john-user

# Or if john user already exists:
python manage.py setup_google_auth --client-id YOUR_CLIENT_ID --client-secret YOUR_CLIENT_SECRET
```

### 6. Test the Setup

1. Start the development server: `python manage.py runserver`
2. Go to the login page
3. Click "Sign in with Google"
4. Try logging in with one of the authorized emails

## Security Features

### Authorized Emails Only
- Only `<EMAIL>` and `<EMAIL>` can log in
- Any other Google account will be rejected with an error message

### User Mapping
- Both authorized Google accounts are automatically mapped to the existing "john" user
- This means both users see the same data and have the same permissions
- All data remains associated with the "john" user account

### Fallback Authentication
- Traditional username/password login is still available as a backup
- The original "john" user account remains functional

## How It Works

1. User clicks "Sign in with Google"
2. Google OAuth flow begins
3. User authenticates with Google
4. System checks if the email is in the authorized list
5. If authorized, the user is mapped to the "john" account
6. If not authorized, access is denied with an error message

## Troubleshooting

### "Target user 'john' not found"
Run: `python manage.py setup_google_auth --create-john-user`

### "Google OAuth app not configured"
Make sure you've run the setup command with valid credentials.

### "Redirect URI mismatch"
Ensure the redirect URI in Google Cloud Console matches your domain exactly.

### Users can't access after Google login
Check that the email addresses in `ALLOWED_GOOGLE_EMAILS` in settings.py match exactly (case-sensitive).

## Production Deployment

1. Update your `.env` file with production Google OAuth credentials
2. Ensure the redirect URI in Google Cloud Console includes your production domain
3. Run migrations: `python manage.py migrate`
4. Run setup: `python manage.py setup_google_auth`
5. Test the login flow

## Maintenance

### Adding New Authorized Users
To add more authorized Google accounts, update the `ALLOWED_GOOGLE_EMAILS` list in `LTDRW/settings.py`:

```python
ALLOWED_GOOGLE_EMAILS = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',  # Add new emails here
]
```

### Removing Access
Simply remove the email from the `ALLOWED_GOOGLE_EMAILS` list and restart the application.

## Security Notes

- All Google-authenticated users share the same "john" account data
- Google OAuth tokens are managed by django-allauth
- Traditional login remains available for emergency access
- Only the specified email addresses can authenticate via Google
- All authentication attempts are logged for security monitoring