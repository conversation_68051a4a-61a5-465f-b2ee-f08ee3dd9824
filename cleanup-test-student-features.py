#!/usr/bin/env python3
"""
Clean up all test data for Test Student Features.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from JW.models import AppJwStudent, AppJwBlockBooking, AppJwLesson, AppJwBlockBookingUsage, AppJwUserSettings
from django.contrib.auth.models import User

def cleanup_test_student_features():
    """Clean up all test data for Test Student Features."""
    
    print("🧹 Cleaning Up Test Student Features Data")
    print("=" * 50)
    
    try:
        # Find Test Student Features
        try:
            student = AppJwStudent.objects.get(student_name='Test Student Features')
            print(f"Found test student: {student.student_name} (ID: {student.id})")
        except AppJwStudent.DoesNotExist:
            print("Test Student Features not found - nothing to clean up")
            return
        
        # Show current test data
        bookings = AppJwBlockBooking.objects.filter(student=student)
        lessons = AppJwLesson.objects.filter(student=student)
        usage_records = AppJwBlockBookingUsage.objects.filter(block_booking__student=student)
        
        print(f"\nCurrent test data:")
        print(f"  Block bookings: {bookings.count()}")
        print(f"  Lessons: {lessons.count()}")
        print(f"  Usage records: {usage_records.count()}")
        
        if bookings.count() > 0:
            print(f"\nBlock bookings to be deleted:")
            for booking in bookings:
                print(f"  - £{booking.amount_paid} ({booking.calculation_method}) - {booking.notes}")
        
        if lessons.count() > 0:
            print(f"\nLessons to be deleted:")
            for lesson in lessons:
                print(f"  - {lesson.date}: {lesson.lesson_hours}h × £{lesson.price_per_hour} - {lesson.notes}")
        
        # Confirm deletion
        print(f"\n⚠️  This will delete ALL test data for Test Student Features")
        print(f"   - {bookings.count()} block bookings")
        print(f"   - {lessons.count()} lessons") 
        print(f"   - {usage_records.count()} usage records")
        print(f"   - Reset student settings to defaults")
        print(f"")
        
        confirm = input("Proceed with cleanup? (y/N): ")
        if confirm.lower() != 'y':
            print("❌ Cleanup cancelled")
            return
        
        # Delete in correct order (foreign key dependencies)
        print(f"\n🗑️ Deleting test data...")
        
        # Delete usage records first
        usage_count = usage_records.count()
        usage_records.delete()
        print(f"✅ Deleted {usage_count} usage records")
        
        # Delete lessons
        lesson_count = lessons.count()
        lessons.delete()
        print(f"✅ Deleted {lesson_count} lessons")
        
        # Delete block bookings
        booking_count = bookings.count()
        bookings.delete()
        print(f"✅ Deleted {booking_count} block bookings")
        
        # Reset student settings
        student.block_booking_disabled = False
        student.save()
        print(f"✅ Reset student settings to defaults")
        
        # Clean up test user settings
        try:
            test_user = User.objects.get(username='test_safe_features')
            test_user_settings = AppJwUserSettings.objects.filter(user=test_user)
            if test_user_settings.exists():
                test_user_settings.delete()
                print(f"✅ Cleaned up test user settings")
            test_user.delete()
            print(f"✅ Deleted test user")
        except User.DoesNotExist:
            pass
        
        # Final verification
        print(f"\n🔍 Final verification:")
        final_bookings = AppJwBlockBooking.objects.filter(student=student).count()
        final_lessons = AppJwLesson.objects.filter(student=student).count()
        final_usage = AppJwBlockBookingUsage.objects.filter(block_booking__student=student).count()
        
        print(f"  Block bookings remaining: {final_bookings}")
        print(f"  Lessons remaining: {final_lessons}")
        print(f"  Usage records remaining: {final_usage}")
        print(f"  Block booking disabled: {student.block_booking_disabled}")
        
        if final_bookings == 0 and final_lessons == 0 and final_usage == 0:
            print(f"\n🎉 Cleanup Complete!")
            print(f"✅ Test Student Features is now clean and ready for future testing")
            print(f"✅ All test data removed")
            print(f"✅ Student settings reset to defaults")
            print(f"✅ No impact on any other data")
        else:
            print(f"\n⚠️ Some data may remain - please check manually")
        
        print(f"\n📋 Test Student Features Status:")
        print(f"   ID: {student.id}")
        print(f"   Name: {student.student_name}")
        print(f"   Area: {student.area}")
        print(f"   Active: {student.active}")
        print(f"   Block booking disabled: {student.block_booking_disabled}")
        print(f"   Ready for future testing: ✅")
        
    except Exception as e:
        print(f"❌ Cleanup failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    cleanup_test_student_features()
