{% extends 'JW/base.html' %}

{% block title %}Fuel Expenses{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 1400px;">
    <!-- Page Header and Stats - Only visible on xl screens -->
    <h2 class="d-none d-xl-block mb-4">Fuel Expenses</h2>
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Total Cost</div>
                    <div class="h4">£{{ total_cost|default:0|floatformat:2 }}</div>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Business Cost</div>
                    <div class="h4">£{{ business_fuel_cost|default:0|floatformat:2 }}</div>
                    <small class="text-muted">({{ business_percentage|floatformat:1 }}%)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Personal Cost</div>
                    <div class="h4">£{{ personal_fuel_cost|default:0|floatformat:2 }}</div>
                    <small class="text-muted">({{ personal_percentage|floatformat:1 }}%)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Fuel Purchase</div>
                    <div class="h4">£{{ avg_cost|default:0|floatformat:2 }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second row of average fuel statistics (moved from mileage page) -->
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Fuel/Week</div>
                    <div class="h5">£{{ avg_fuel_cost_per_week_alltime|floatformat:2 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Fuel/Month</div>
                    <div class="h5">£{{ avg_fuel_cost_per_month_alltime|floatformat:2 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Fuel/Quarter</div>
                    <div class="h5">£{{ avg_fuel_cost_per_quarter_alltime|floatformat:2 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Third row of current period fuel statistics (moved from mileage page) -->
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Week Fuel</div>
                    <div class="h5">£{{ current_week_business_fuel_cost|floatformat:2 }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Month Fuel</div>
                    <div class="h5">£{{ current_month_business_fuel_cost|floatformat:2 }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Quarter Fuel</div>
                    <div class="h5">£{{ current_quarter_business_fuel_cost|floatformat:2 }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{% url 'fuel_expense_create' %}" class="btn btn-primary w-100 h-100 d-flex align-items-center justify-content-center py-2">
            <div class="text-center">
                <i class="fas fa-gas-pump fa-2x mb-2"></i>
                <div>Add New Fuel Expense</div>
            </div>
        </a>
    </div>

    <!-- Wrap table in a div that respects container width -->
    <div style="min-width: auto; overflow-x: auto;">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th style="width: 20%">Date</th>
                    <th style="width: 15%">Day</th>
                    <th style="width: 15%">Cost</th>
                    <th style="width: 50%">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for expense in expenses %}
                <tr>
                    <td>{{ expense.date|date:"M d, Y" }}</td>
                    <td>{{ expense.day_of_week }}</td>
                    <td>£{{ expense.cost|floatformat:2 }}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'fuel_expense_update' expense.pk %}" class="btn btn-sm btn-warning">Edit</a>
                            <a href="{% url 'fuel_expense_delete' expense.pk %}" class="btn btn-sm btn-danger">Delete</a>
                            {% if expense.notes %}
                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#notesModal{{ expense.pk }}">
                                    View Notes
                                </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>

                <!-- Notes Modal -->
                {% if expense.notes %}
                <div class="modal fade" id="notesModal{{ expense.pk }}" tabindex="-1" aria-labelledby="notesModalLabel{{ expense.pk }}" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="notesModalLabel{{ expense.pk }}">Notes for {{ expense.date|date:"M d, Y" }}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                {{ expense.notes }}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                {% empty %}
                <tr>
                    <td colspan="4" class="text-center">No fuel expenses found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
