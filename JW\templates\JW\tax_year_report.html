{% extends 'JW/base.html' %}

{% block title %}Tax Year Report {{ tax_year_display }}{% endblock %}

{% block content %}
<div class="container mt-4">
    {% if report_header %}
    <div class="text-center mb-4">
        {% if report_header.name %}<h3 class="mb-2">{{ report_header.name }}</h3>{% endif %}
        {% if report_header.address %}<p class="mb-2">{{ report_header.address }}</p>{% endif %}
        {% if report_header.phone %}<p class="mb-2">{{ report_header.phone }}</p>{% endif %}
        {% if report_header.email %}<p class="mb-2">{{ report_header.email }}</p>{% endif %}
        {% if report_header.website %}<p class="mb-2">{{ report_header.website }}</p>{% endif %}
        {% if report_header.business_number %}<p class="mb-2">Business Number: {{ report_header.business_number }}</p>{% endif %}
    </div>
    {% endif %}
    
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Tax Year Report {{ tax_year_display }}</h2>
        <div>
            <a href="{% url 'tax_year_report_pdf' %}?start_year={{ start_date.year }}" class="btn btn-success">
                <i class="fas fa-download"></i> Download PDF
            </a>
            <a href="{% url 'report_list' %}" class="btn btn-secondary ms-2">
                Back to Reports
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h4 class="mb-0">Income Summary</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped w-100">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Student</th>
                            <th>Hours</th>
                            <th>Price/Hour</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for lesson in lessons %}
                        <tr>
                            <td>{{ lesson.date|date:"d/m/Y" }}</td>
                            <td>{{ lesson.student }}</td>
                            <td>{{ lesson.lesson_hours }}</td>
                            <td>£{{ lesson.price_per_hour }}</td>
                            <td class="text-end">£{{ lesson.amount }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-success">
                            <th colspan="4">Total Income:</th>
                            <th class="text-end">£{{ total_income|floatformat:2 }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h4 class="mb-0">Business Expenses</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped w-100">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Type</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for expense in business_expenses %}
                        <tr>
                            <td>{{ expense.date|date:"d/m/Y" }}</td>
                            <td>{{ expense.description }}</td>
                            <td>{{ expense.expense_type }}</td>
                            <td class="text-end">£{{ expense.cost|floatformat:2 }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-danger">
                            <th colspan="3">Total Business Expenses:</th>
                            <th class="text-end">£{{ total_business_expenses|floatformat:2 }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h4 class="mb-0">Fuel Expenses</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped w-100">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Litres</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for expense in fuel_expenses %}
                        <tr>
                            <td>{{ expense.date|date:"d/m/Y" }}</td>
                            <td>{{ expense.litres|floatformat:1 }}</td>
                            <td class="text-end">£{{ expense.cost|floatformat:2 }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-warning">
                            <th colspan="2">Total Fuel Expenses:</th>
                            <th class="text-end">£{{ total_fuel_expenses|floatformat:2 }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h4 class="mb-0">Mileage Summary</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <tbody>
                    <tr>
                        <th>Total Miles:</th>
                        <td class="text-end">{{ total_miles|floatformat:1 }}</td>
                    </tr>
                    <tr>
                        <th>Business Miles:</th>
                        <td class="text-end">{{ total_business_miles|floatformat:1 }} ({{ business_miles_percentage|floatformat:1 }}%)</td>
                    </tr>
                    <tr>
                        <th>Personal Miles:</th>
                        <td class="text-end">{{ total_personal_miles|floatformat:1 }} ({{ personal_miles_percentage|floatformat:1 }}%)</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">Financial Summary</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <tbody>
                    <tr class="table-success">
                        <th>Total Income</th>
                        <td class="text-end">£{{ total_income|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-danger">
                        <th>Total Business Expenses</th>
                        <td class="text-end">£{{ total_business_expenses|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-warning">
                        <th>Total Fuel Expenses</th>
                        <td class="text-end">£{{ total_fuel_expenses|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-primary">
                        <th>Net Income</th>
                        <td class="text-end">£{{ net_income|floatformat:2 }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
