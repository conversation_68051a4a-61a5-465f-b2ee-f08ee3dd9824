# LTDRW Project

A Django 4.2 project with PostgreSQL integration.

## Setup

1. Create and activate virtual environment:
```bash
python3 -m venv venv
source venv/bin/activate
```

2. Install dependencies:
```bash
pip install --break-system-packages -r requirements.txt
```

3. Run migrations:
```bash
python3 manage.py migrate
```

4. Run the development server:
```bash
python3 manage.py runserver
```

## Features
- Django 4.2
- PostgreSQL database integration
- Django Crispy Forms with Bootstrap 5
- PDF generation with ReportLab and xhtml2pdf
- Static file serving with WhiteNoise
- Production-ready with Gunicorn
- JW app integration

## Recent Optimizations
- **Dependencies optimized**: Reduced from 46 to 14 essential packages
- **Improved maintainability**: Removed redundant and unused dependencies
- **Enhanced security**: Reduced attack surface by removing unnecessary packages
- **Faster installation**: Streamlined dependency tree
- See `DEPENDENCY_OPTIMIZATION_REPORT.md` for detailed analysis
