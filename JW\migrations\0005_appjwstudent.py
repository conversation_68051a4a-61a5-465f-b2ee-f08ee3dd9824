# Generated by Django 4.2 on 2025-02-20 14:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('JW', '0004_appjwstudent'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppJwStudent',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('student_name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=30, unique=True)),
                ('mobile_number', models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                ('email_address', models.EmailField(blank=True, max_length=100, null=True)),
                ('address_1st_line', models.CharField(blank=True, max_length=100, null=True)),
                ('address_2nd_line', models.CharField(blank=True, max_length=100, null=True)),
                ('area', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('post_code', models.Char<PERSON><PERSON>(blank=True, max_length=10, null=True)),
                ('gender', models.Char<PERSON><PERSON>(blank=True, choices=[('Male', 'Male'), ('Female', 'Female')], max_length=6, null=True)),
                ('age', models.IntegerField(blank=True, null=True)),
                ('active', models.CharField(choices=[('Yes', 'Yes'), ('No', 'No')], default='Yes', max_length=3)),
                ('notes', models.TextField(blank=True, null=True)),
                ('test_past', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'APP_JW_student',
                'ordering': ['student_name'],
                'managed': False,
            },
        ),
    ]
