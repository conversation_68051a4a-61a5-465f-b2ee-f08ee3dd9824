#!/bin/bash

# LTDWJ Deployment Test Script
# This script helps test the deployment and diagnose issues

echo "🔍 LTDWJ Deployment Diagnostics"
echo "================================"

# Get today's container name
CONTAINER_NAME="LTDWJ-$(date +%d%m%Y)"

echo "📅 Looking for container: $CONTAINER_NAME"

# Check if container exists and is running
if docker ps | grep -q $CONTAINER_NAME; then
    echo "✅ Container $CONTAINER_NAME is running"
    
    echo ""
    echo "📊 Container Status:"
    docker ps | grep $CONTAINER_NAME
    
    echo ""
    echo "🔍 Testing application health..."
    
    # Test the health endpoint
    echo "Testing health endpoint..."
    curl -s http://**********:8002/health/ | python3 -m json.tool 2>/dev/null || echo "❌ Health endpoint failed"
    
    echo ""
    echo "Testing main application..."
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://**********:8002/)
    if [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ Application responding with HTTP 200"
    else
        echo "❌ Application responding with HTTP $HTTP_STATUS"
    fi
    
    echo ""
    echo "📋 Recent Container Logs (last 20 lines):"
    echo "----------------------------------------"
    docker logs --tail 20 $CONTAINER_NAME
    
elif docker ps -a | grep -q $CONTAINER_NAME; then
    echo "⚠️  Container $CONTAINER_NAME exists but is not running"
    
    echo ""
    echo "📋 Container Logs:"
    echo "----------------"
    docker logs $CONTAINER_NAME
    
    echo ""
    echo "🔄 Attempting to start container..."
    docker start $CONTAINER_NAME
    
    sleep 5
    
    if docker ps | grep -q $CONTAINER_NAME; then
        echo "✅ Container started successfully"
    else
        echo "❌ Container failed to start"
        echo "📋 Latest logs:"
        docker logs --tail 10 $CONTAINER_NAME
    fi
    
else
    echo "❌ Container $CONTAINER_NAME not found"
    echo ""
    echo "📋 Available containers:"
    docker ps -a | grep LTDWJ || echo "No LTDWJ containers found"
    
    echo ""
    echo "🔨 You may need to run the deployment script:"
    echo "./deploy.sh"
fi

echo ""
echo "🔧 Useful Commands:"
echo "==================="
echo "View logs:     docker logs $CONTAINER_NAME"
echo "Follow logs:   docker logs -f $CONTAINER_NAME"
echo "Stop:          docker stop $CONTAINER_NAME"
echo "Start:         docker start $CONTAINER_NAME"
echo "Restart:       docker restart $CONTAINER_NAME"
echo "Shell access:  docker exec -it $CONTAINER_NAME /bin/bash"
echo "Redeploy:      ./deploy.sh"

echo ""
echo "🌐 Application URLs:"
echo "==================="
echo "Main app:      http://**********:8002/"
echo "Health check:  http://**********:8002/health/"
echo "Admin:         http://**********:8002/admin/"
