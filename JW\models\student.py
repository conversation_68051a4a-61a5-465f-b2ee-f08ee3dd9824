from django.db import models
from django.contrib.auth.models import User

class AppJwStudent(models.Model):
    id = models.BigAutoField(primary_key=True)
    student_name = models.CharField(max_length=30, unique=True)
    mobile_number = models.CharField(max_length=20, blank=True, null=True)
    email_address = models.CharField(max_length=100, blank=True, null=True)
    address_1st_line = models.CharField(max_length=100, blank=True, null=True)
    address_2nd_line = models.CharField(max_length=100, blank=True, null=True)
    area = models.CharField(max_length=50, blank=True, null=True)
    post_code = models.CharField(max_length=10, blank=True, null=True)
    gender = models.CharField(max_length=6, blank=True, null=True)
    age = models.IntegerField(blank=True, null=True)
    active = models.CharField(
        max_length=6,
        default='Yes',
        choices=[
            ('Yes', 'Yes'),
            ('No', 'No'),
            ('Passed', 'Passed')
        ]
    )
    notes = models.TextField(blank=True, null=True)
    test_past = models.DateField(blank=True, null=True)
    test_centre = models.ForeignKey('AppJwTestCentre', models.DO_NOTHING, null=True, blank=True)
    block_booking_disabled = models.BooleanField(
        default=False,
        help_text="Disable block booking for this student (pay per lesson)"
    )

    class Meta:
        managed = False
        db_table = 'APP_JW_student'
        ordering = ['student_name']

    def __str__(self):
        return self.student_name
