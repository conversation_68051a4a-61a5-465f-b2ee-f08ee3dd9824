{% extends "JW/base.html" %}
{% load crispy_forms_tags %}
{% load socialaccount %}

{% block title %}Login{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card mt-4">
            <div class="card-body">
                <h2 class="card-title text-center mb-4">Login to Driving School Management</h2>
                
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
                
                <!-- Google OAuth Login -->
                <div class="text-center mb-4">
                    {% get_providers as socialaccount_providers %}
                    {% for provider in socialaccount_providers %}
                        {% if provider.id == "google" %}
                            <a href="{% provider_login_url 'google' %}" class="btn btn-danger btn-lg w-100 mb-3">
                                <i class="fab fa-google me-2"></i>
                                Sign in with Google
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
                
                <hr class="my-4">
                
                <!-- Traditional Login (kept as backup) -->
                <div class="text-center mb-3">
                    <small class="text-muted">Or use traditional login</small>
                </div>
                
                {% if form.errors %}
                <div class="alert alert-danger">
                    Your username and password didn't match. Please try again.
                </div>
                {% endif %}
                
                {% if next %}
                    {% if user.is_authenticated %}
                    <div class="alert alert-info">
                        Your account doesn't have access to this page. To proceed,
                        please login with an account that has access.
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        Please login to see this page.
                    </div>
                    {% endif %}
                {% endif %}
                
                <form method="post" action="{% url 'account_login' %}">
                    {% csrf_token %}
                    {{ form|crispy }}
                    <input type="hidden" name="next" value="{{ next }}">
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-outline-primary">Login with Username/Password</button>
                    </div>
                </form>
                
<!--                 <div class="text-center mt-3">
                    <small class="text-muted">
                        Only authorized users (markasmith101@gmail.<NAME_EMAIL>) can access this system.
                    </small>
                </div> -->
            </div>
        </div>
    </div>
</div>
{% endblock %}
