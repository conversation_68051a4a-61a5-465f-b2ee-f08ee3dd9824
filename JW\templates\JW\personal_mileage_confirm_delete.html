{% extends 'JW/base.html' %}

{% block title %}Delete Personal Mileage{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">Delete Personal Mileage</h2>
                <p class="card-text">Are you sure you want to delete the personal mileage record of {{ mileage.mileage }} miles on {{ mileage.date }}?</p>
                <form method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                    <a href="{% url 'personal_mileage_list' %}" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
