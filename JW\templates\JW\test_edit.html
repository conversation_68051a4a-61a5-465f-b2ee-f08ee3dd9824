{% extends 'JW/base.html' %}

{% block title %}Edit Test Centre{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Edit Test Centre</h4>
                </div>
                <div class="card-body">
                    {% if failed_test %}
                        <div class="alert alert-info">
                            <h6><i class="fas fa-times-circle text-danger"></i> Failed Test</h6>
                            <p class="mb-0">
                                <strong>Student:</strong> {{ failed_test.student_name }}<br>
                                <strong>Test Date:</strong> {{ failed_test.test_date|date:"M d, Y" }}<br>
                                <strong>Current Test Centre:</strong> 
                                {% if failed_test.test_centre %}
                                    {{ failed_test.test_centre.name }}
                                {% else %}
                                    <span class="text-muted">Not specified</span>
                                {% endif %}
                            </p>
                        </div>
                    {% elif passed_student %}
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle text-success"></i> Passed Test</h6>
                            <p class="mb-0">
                                <strong>Student:</strong> {{ passed_student.student_name }}<br>
                                <strong>Test Date:</strong> {{ passed_student.test_past|date:"M d, Y" }}<br>
                                <strong>Current Test Centre:</strong> 
                                {% if passed_student.test_centre %}
                                    {{ passed_student.test_centre.name }}
                                {% else %}
                                    <span class="text-muted">Not specified</span>
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="test_date" class="form-label">Test Date</label>
                            <input type="date" name="test_date" id="test_date" class="form-control"
                                {% if failed_test %}value="{{ failed_test.test_date|date:'Y-m-d' }}"{% endif %}
                                {% if passed_student %}value="{{ passed_student.test_past|date:'Y-m-d' }}"{% endif %}>
                            <small class="form-text text-muted">Leave blank to keep current date</small>
                        </div>

                        <div class="mb-3">
                            <label for="test_centre" class="form-label">Select Test Centre</label>
                            <select name="test_centre" id="test_centre" class="form-select" required>
                                <option value="">Choose a test centre...</option>
                                {% for centre in test_centres %}
                                    <option value="{{ centre.id }}"
                                        {% if failed_test and failed_test.test_centre.id == centre.id %}selected{% endif %}
                                        {% if passed_student and passed_student.test_centre.id == centre.id %}selected{% endif %}>
                                        {{ centre.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        {% if not test_centres %}
                            <div class="alert alert-warning">
                                <p class="mb-2">No test centres available. Please add test centres first.</p>
                                <a href="{% url 'manage_report_header' %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> Add Test Centres
                                </a>
                            </div>
                        {% endif %}

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'tests_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Tests
                            </a>
                            {% if test_centres %}
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Test Details
                                </button>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
