# Django
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
db.sqlite3-journal

# Environment variables
.env
.env.local
.env.production

# Static files
/staticfiles/
/static/
/media/

# Virtual environments
venv/
venv_test/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.backup
requirements.txt.backup

# Docker
.dockerignore

# Logs
django.log
*.log

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# pytest
.pytest_cache/

# mypy
.mypy_cache/
