# Generated by Django 4.2 on 2025-05-25 15:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('JW', '0007_block_booking_tables'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppJwBlockBooking',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('date_created', models.DateField()),
                ('amount_paid', models.DecimalField(decimal_places=2, max_digits=6)),
                ('total_lessons', models.DecimalField(decimal_places=1, max_digits=4)),
                ('lessons_used', models.DecimalField(decimal_places=1, default=0, max_digits=4)),
                ('active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'APP_JW_block_booking',
                'ordering': ['-date_created'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppJwBlockBookingUsage',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('lessons_used', models.DecimalField(decimal_places=1, max_digits=4)),
                ('date_used', models.DateField()),
            ],
            options={
                'db_table': 'APP_JW_block_booking_usage',
                'ordering': ['-date_used'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppJwUserSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tax_ni_percentage', models.DecimalField(decimal_places=2, default=21.0, max_digits=5)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='jw_settings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'APP_JW_user_settings',
            },
        ),
    ]
