from django.core.management.base import BaseCommand
from django.db.models import Q, F, Count, Sum
from JW.models import AppJwLesson, AppJwStudent

class Command(BaseCommand):
    help = 'Fixes student statistics for students with missing stats'

    def handle(self, *args, **options):
        self.stdout.write("Starting student statistics check...")
        
        # Get all students
        students = AppJwStudent.objects.all()
        self.stdout.write(f"Found {students.count()} total students")

        for student in students:
            # Get raw lesson count by student_name
            raw_lessons = AppJwLesson.objects.filter(student_name=student.student_name)
            raw_lesson_count = raw_lessons.count()
            
            # Get current stats
            student_stats = AppJwStudent.objects.filter(id=student.id).annotate(
                lesson_count=Count('lessons'),
                total_hours=Sum('lessons__lesson_hours'),
                total_amount=Sum(F('lessons__lesson_hours') * F('lessons__price_per_hour'))
            ).first()
            
            self.stdout.write(f"\nChecking student: {student.student_name}")
            self.stdout.write(f"Raw lessons by name: {raw_lesson_count}")
            self.stdout.write(f"Lessons in stats: {student_stats.lesson_count or 0}")
            
            if raw_lesson_count != (student_stats.lesson_count or 0):
                self.stdout.write(self.style.WARNING("Mismatch found - fixing..."))
                
                # Fix each lesson
                fixed_count = 0
                for lesson in raw_lessons:
                    if lesson.student_id != student.id:
                        lesson.student = student
                        lesson.save()
                        fixed_count += 1
                
                if fixed_count > 0:
                    self.stdout.write(
                        self.style.SUCCESS(f"Fixed {fixed_count} lessons for {student.student_name}")
                    )
                    
                    # Show updated stats
                    updated_stats = AppJwStudent.objects.filter(id=student.id).annotate(
                        lesson_count=Count('lessons'),
                        total_hours=Sum('lessons__lesson_hours'),
                        total_amount=Sum(F('lessons__lesson_hours') * F('lessons__price_per_hour'))
                    ).first()
                    
                    self.stdout.write(
                        f"Updated stats:\n"
                        f"  - Lessons: {updated_stats.lesson_count or 0}\n"
                        f"  - Hours: {updated_stats.total_hours or 0}\n"
                        f"  - Amount: £{updated_stats.total_amount or 0}"
                    )
            else:
                self.stdout.write(self.style.SUCCESS("Stats are correct"))

        self.stdout.write(self.style.SUCCESS("\nCompleted student statistics check and fix"))
