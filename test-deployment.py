#!/usr/bin/env python3
"""
Test script to validate deployment configuration and environment setup.
"""

import os
import sys
import subprocess
from pathlib import Path

class DeploymentTester:
    def __init__(self):
        self.test_results = []
        
    def log_test_result(self, test_name, passed, message=""):
        result = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append((test_name, passed, message))
        print(f"{result}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_env_file_exists(self):
        """Test if .env file exists and has required variables"""
        print("\n📄 Testing Environment File...")
        
        env_file = Path('.env')
        if not env_file.exists():
            self.log_test_result("Environment File Exists", False, ".env file not found")
            return
        
        # Read .env file and check for required variables
        required_vars = [
            'DEBUG', 'SECRET_KEY', 'DB_NAME', 'DB_USER', 'DB_PASSWORD',
            'DB_HOST', 'DB_PORT', 'GOOGLE_OAUTH2_CLIENT_ID', 'GOOGLE_OAUTH2_CLIENT_SECRET'
        ]
        
        with open('.env', 'r', encoding='utf-8') as f:
            env_content = f.read()
        
        missing_vars = []
        for var in required_vars:
            if f"{var}=" not in env_content:
                missing_vars.append(var)
        
        self.log_test_result(
            "Environment Variables Present",
            len(missing_vars) == 0,
            f"Missing variables: {missing_vars}" if missing_vars else "All required variables present"
        )
    
    def test_deployment_script(self):
        """Test deployment script syntax and configuration"""
        print("\n🚀 Testing Deployment Script...")
        
        deploy_script = Path('deploy.sh')
        if not deploy_script.exists():
            self.log_test_result("Deployment Script Exists", False, "deploy.sh not found")
            return
        
        # Check if script is executable
        is_executable = os.access('deploy.sh', os.X_OK)
        self.log_test_result("Deployment Script Executable", is_executable)
        
        # Check script content for security improvements
        with open('deploy.sh', 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        security_checks = {
            'Uses environment variables': '--env-file .env' in script_content or '-e ' in script_content,
            'No hardcoded credentials': 'GOCSPX-' not in script_content,
            'Checks for .env file': 'if [ ! -f .env ]' in script_content,
            'Uses variable substitution': '$' in script_content and 'DEPLOY_' in script_content
        }
        
        passed_security = sum(security_checks.values())
        total_security = len(security_checks)
        
        self.log_test_result(
            "Deployment Script Security",
            passed_security >= 3,  # At least 3 out of 4 security checks
            f"Passed {passed_security}/{total_security} security checks"
        )
    
    def test_docker_configuration(self):
        """Test Docker configuration"""
        print("\n🐳 Testing Docker Configuration...")
        
        dockerfile = Path('Dockerfile')
        if not dockerfile.exists():
            self.log_test_result("Dockerfile Exists", False, "Dockerfile not found")
            return
        
        with open('Dockerfile', 'r', encoding='utf-8') as f:
            dockerfile_content = f.read()
        
        docker_checks = {
            'Has Python base image': 'FROM python:' in dockerfile_content,
            'Sets working directory': 'WORKDIR' in dockerfile_content,
            'Installs requirements': 'requirements.txt' in dockerfile_content,
            'Exposes port': 'EXPOSE' in dockerfile_content,
            'Has CMD instruction': 'CMD' in dockerfile_content
        }
        
        passed_docker = sum(docker_checks.values())
        total_docker = len(docker_checks)
        
        self.log_test_result(
            "Dockerfile Configuration",
            passed_docker >= 4,  # At least 4 out of 5 checks
            f"Passed {passed_docker}/{total_docker} Docker checks"
        )
    
    def test_requirements_file(self):
        """Test requirements.txt file"""
        print("\n📦 Testing Requirements File...")
        
        req_file = Path('requirements.txt')
        if not req_file.exists():
            self.log_test_result("Requirements File Exists", False, "requirements.txt not found")
            return
        
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            requirements = f.read()
        
        essential_packages = [
            'Django', 'psycopg2-binary', 'gunicorn', 'whitenoise',
            'django-allauth', 'python-dotenv'
        ]
        
        missing_packages = []
        for package in essential_packages:
            if package.lower() not in requirements.lower():
                missing_packages.append(package)
        
        self.log_test_result(
            "Essential Packages Present",
            len(missing_packages) == 0,
            f"Missing packages: {missing_packages}" if missing_packages else "All essential packages present"
        )
    
    def test_static_media_directories(self):
        """Test static and media directory setup"""
        print("\n📁 Testing Static and Media Directories...")
        
        static_dir = Path('staticfiles')
        media_dir = Path('media')
        
        # These directories should exist or be creatable
        try:
            static_dir.mkdir(exist_ok=True)
            media_dir.mkdir(exist_ok=True)
            
            self.log_test_result("Static/Media Directories", True, "Directories created/verified")
        except Exception as e:
            self.log_test_result("Static/Media Directories", False, str(e))
    
    def test_mcp_configuration(self):
        """Test MCP configuration files"""
        print("\n🔧 Testing MCP Configuration...")
        
        mcp_configs = [
            'mcp-configs/cursor-mcp.json',
            'mcp-configs/vscode-settings.json',
            'mcp-configs/claude-desktop-config.json'
        ]
        
        existing_configs = []
        for config in mcp_configs:
            if Path(config).exists():
                existing_configs.append(config)
        
        self.log_test_result(
            "MCP Configuration Files",
            len(existing_configs) >= 1,
            f"Found {len(existing_configs)} MCP configuration files"
        )
        
        # Check if rules files exist
        rules_files = ['.cursorrules', '.windsurfrules']
        existing_rules = [f for f in rules_files if Path(f).exists()]
        
        self.log_test_result(
            "Editor Rules Files",
            len(existing_rules) >= 1,
            f"Found rules files: {existing_rules}"
        )
    
    def test_logging_directories(self):
        """Test that log files can be created"""
        print("\n📝 Testing Logging Setup...")
        
        try:
            # Test if we can create log files
            test_log = Path('test.log')
            test_log.write_text('test')
            test_log.unlink()  # Remove test file
            
            self.log_test_result("Log File Creation", True, "Can create log files")
        except Exception as e:
            self.log_test_result("Log File Creation", False, str(e))
    
    def run_all_tests(self):
        """Run all deployment tests"""
        print("🧪 Starting Deployment Test Suite")
        print("=" * 40)
        
        self.test_env_file_exists()
        self.test_deployment_script()
        self.test_docker_configuration()
        self.test_requirements_file()
        self.test_static_media_directories()
        self.test_mcp_configuration()
        self.test_logging_directories()
        
        # Summary
        print("\n📊 Deployment Test Results")
        print("=" * 30)
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 All deployment tests passed! Ready for production deployment.")
        else:
            print("\n⚠️ Some deployment tests failed. Please review the issues above.")
            
        return passed == total

if __name__ == "__main__":
    tester = DeploymentTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
