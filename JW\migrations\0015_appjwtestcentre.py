# Generated by Django 4.2 on 2025-06-30 18:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('JW', '0014_add_performance_indexes'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppJwTestCentre',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('active', models.BooleanField(default=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'APP_JW_test_centre',
                'ordering': ['name'],
                'unique_together': {('name', 'user')},
            },
        ),
    ]
