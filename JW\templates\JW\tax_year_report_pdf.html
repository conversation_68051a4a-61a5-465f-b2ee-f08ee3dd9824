<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.3;
            margin: 20px;
        }
        .container-fluid {
            padding: 0 20px;
        }
        .card {
            border: 1px solid #ddd;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .card-header {
            padding: 10px 15px;
            margin-bottom: 0;
        }
        .bg-success {
            background-color: #198754 !important;
            color: white;
        }
        .bg-danger {
            background-color: #dc3545 !important;
            color: white;
        }
        .bg-info {
            background-color: #0dcaf0 !important;
            color: white;
        }
        .bg-primary {
            background-color: #0d6efd !important;
            color: white;
        }
        .bg-warning {
            background-color: #ffc107 !important;
            color: black;
        }
        .card-body {
            padding: 15px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
        }
        .table th, .table td {
            padding: 8px;
            border: 1px solid #dee2e6;
        }
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0,0,0,.05);
        }
        .text-end {
            text-align: right;
        }
        .table-success {
            background-color: #d1e7dd;
        }
        .table-danger {
            background-color: #f8d7da;
        }
        .table-info {
            background-color: #cff4fc;
        }
        .table-primary {
            background-color: #cfe2ff;
        }
        .mb-4 {
            margin-bottom: 1.5rem;
        }
        .h4 {
            margin: 0;
            font-size: 1.5rem;
        }
        .header-section {
            text-align: center;
            margin-bottom: 30px;
        }
        .header-section h2 {
            margin-bottom: 8px;
        }
        .header-section p {
            margin: 0 0 8px 0;
        }
        h1 {
            text-align: center;
            margin: 20px 0 30px 0;
        }
    </style>
</head>
<body>
    {% if report_header %}
    <div class="header-section">
        {% if report_header.name %}<h2>{{ report_header.name }}</h2>{% endif %}
        {% if report_header.address %}<p>{{ report_header.address }}</p>{% endif %}
        {% if report_header.phone %}<p>{{ report_header.phone }}</p>{% endif %}
        {% if report_header.email %}<p>{{ report_header.email }}</p>{% endif %}
        {% if report_header.website %}<p>{{ report_header.website }}</p>{% endif %}
        {% if report_header.business_number %}<p>Business Number: {{ report_header.business_number }}</p>{% endif %}
    </div>
    {% endif %}

    <h1>Tax Year Report {{ tax_year_display }}</h1>

    <div class="card mb-4">
        <div class="card-header bg-success">
            <h4 class="mb-0">Income Summary</h4>
        </div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Student</th>
                        <th>Hours</th>
                        <th>Price/Hour</th>
                        <th class="text-end">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for lesson in lessons %}
                    <tr>
                        <td>{{ lesson.date|date:"d/m/Y" }}</td>
                        <td>{{ lesson.student_name }}</td>
                        <td>{{ lesson.lesson_hours }}</td>
                        <td>£{{ lesson.price_per_hour|floatformat:2 }}</td>
                        <td class="text-end">£{{ lesson.amount|default:0|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                    <tr class="table-success">
                        <td colspan="4"><strong>Total Income</strong></td>
                        <td class="text-end"><strong>£{{ total_income|floatformat:2 }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-danger">
            <h4 class="mb-0">Business Expenses</h4>
        </div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Type</th>
                        <th class="text-end">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for expense in business_expenses %}
                    <tr>
                        <td>{{ expense.date|date:"d/m/Y" }}</td>
                        <td>{{ expense.expense_type }}</td>
                        <td class="text-end">£{{ expense.cost|default:0|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                    <tr class="table-danger">
                        <td colspan="2"><strong>Total Business Expenses</strong></td>
                        <td class="text-end"><strong>£{{ total_business_expenses|floatformat:2 }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-info">
            <h4 class="mb-0">Fuel Expenses</h4>
        </div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th class="text-end">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for expense in fuel_expenses %}
                    <tr>
                        <td>{{ expense.date|date:"d/m/Y" }}</td>
                        <td class="text-end">£{{ expense.cost|default:0|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                    <tr class="table-info">
                        <td><strong>Total Fuel Expenses</strong></td>
                        <td class="text-end"><strong>£{{ total_fuel_expenses|floatformat:2 }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-warning">
            <h4 class="mb-0">Mileage Summary</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <tbody>
                    <tr>
                        <th>Total Miles:</th>
                        <td class="text-end">{{ total_miles|floatformat:1 }}</td>
                    </tr>
                    <tr>
                        <th>Business Miles:</th>
                        <td class="text-end">{{ total_business_miles|floatformat:1 }} ({{ business_miles_percentage|floatformat:1 }}%)</td>
                    </tr>
                    <tr>
                        <th>Personal Miles:</th>
                        <td class="text-end">{{ total_personal_miles|floatformat:1 }} ({{ personal_miles_percentage|floatformat:1 }}%)</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary">
            <h4 class="mb-0">Financial Summary</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <tbody>
                    <tr class="table-success">
                        <th>Total Income</th>
                        <td class="text-end">£{{ total_income|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-danger">
                        <th>Total Business Expenses</th>
                        <td class="text-end">£{{ total_business_expenses|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-info">
                        <th>Total Fuel Expenses</th>
                        <td class="text-end">£{{ total_fuel_expenses|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-primary">
                        <th><strong>Net Income</strong></th>
                        <td class="text-end"><strong>£{{ net_income|floatformat:2 }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
