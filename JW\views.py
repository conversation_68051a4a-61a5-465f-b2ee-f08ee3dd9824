from django.http import HttpResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.conf import settings
from django.urls import reverse
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import (
    Sum, F, Q, Count, Case, When, Subquery, OuterRef, Max, Value,
    DecimalField, FloatField, Exists
)
from django.db.models.functions import Coalesce
from django.template.loader import render_to_string, get_template
from django.core.cache import cache
from django.utils import timezone
from datetime import datetime, date, timedelta
from dateutil.relativedelta import relativedelta
import calendar
from decimal import Decimal
from xhtml2pdf import pisa
from io import BytesIO
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from .models import (
    AppJwStudent,
    AppJwLesson,
    AppJwBusinessexpense,
    AppJwFuelexpense,
    AppJwMileage,
    AppJwBusinessmileage,
    AppJwPersonalmileage,
    AppJwReportHeader,
    AppJwFailedTest,
    AppJwTestCentre,
    AppJwBlockBooking,
    AppJwBlockBookingUsage
)
from .forms import (
    LessonForm,
    StudentForm,
    BusinessExpenseForm,
    FuelExpenseForm,
    MileageForm,
    BusinessMileageForm,
    PersonalMileageForm,
    BlockBookingForm,
    TestCentreForm
)
from django.db import IntegrityError, transaction
from django.core.exceptions import ValidationError
from django.conf import settings
from urllib.parse import urlparse
from django.views.decorators.cache import cache_page
from django.http import JsonResponse
from django.db import connection
from django.core.exceptions import DatabaseError
import logging

# Import our custom error handling decorators
from .utils.error_handling import (
    handle_database_errors,
    handle_form_errors,
    handle_block_booking_errors,
    log_user_action
)

logger = logging.getLogger(__name__)

def get_lessons_for_period(start_date, end_date):
    """Helper function to get lessons for a given period"""
    return AppJwLesson.objects.select_related('student').filter(
        date__gte=start_date,
        date__lte=end_date
    ).order_by('-date')

def get_stats(start_date=None, end_date=None):
    """Helper function to get real-time statistics"""
    # Base queryset filters
    lesson_filter = {}
    expense_filter = {}
    fuel_filter = {}
    mileage_filter = {}

    if start_date:
        lesson_filter['date__gte'] = start_date
        expense_filter['date__gte'] = start_date
        fuel_filter['date__gte'] = start_date
        mileage_filter['date__gte'] = start_date

    if end_date:
        lesson_filter['date__lte'] = end_date
        expense_filter['date__lte'] = end_date
        fuel_filter['date__lte'] = end_date
        mileage_filter['date__lte'] = end_date

    # Get lesson statistics
    lesson_stats = AppJwLesson.objects.filter(**lesson_filter).aggregate(
        total_income=Sum(
            Case(
                When(amount__isnull=False, then='amount'),
                default=F('lesson_hours') * F('price_per_hour'),
                output_field=DecimalField()
            )
        ),
        total_hours=Sum('lesson_hours'),
        lesson_count=Count('id'),
        unique_students=Count('student', distinct=True)
    )

    # Get expense statistics
    expense_stats = AppJwBusinessexpense.objects.filter(**expense_filter).aggregate(
        total_expenses=Sum('cost')
    )

    # Get fuel expense statistics
    fuel_stats = AppJwFuelexpense.objects.filter(**fuel_filter).aggregate(
        total_fuel=Sum('cost')
    )

    # Get mileage statistics
    mileage_stats = AppJwMileage.objects.filter(**mileage_filter).aggregate(
        total_miles=Sum('miles'),
        total_business_miles=Sum(Case(
            When(mileage_type='Business', then='miles'),
            default=0,
            output_field=DecimalField()
        )),
        total_personal_miles=Sum(Case(
            When(mileage_type='Personal', then='miles'),
            default=0,
            output_field=DecimalField()
        ))
    )

    # Calculate total expenses
    total_expenses = Decimal('0.00')
    if expense_stats['total_expenses']:
        total_expenses += expense_stats['total_expenses']
    if fuel_stats['total_fuel']:
        total_expenses += fuel_stats['total_fuel']

    # Ensure all values are Decimal or 0
    total_income = lesson_stats['total_income'] or Decimal('0.00')
    total_hours = lesson_stats['total_hours'] or Decimal('0.00')
    lesson_count = lesson_stats['lesson_count'] or 0
    unique_students = lesson_stats['unique_students'] or 0

    return {
        'total_income': total_income,
        'total_hours': total_hours,
        'lesson_count': lesson_count,
        'unique_students': unique_students,
        'total_expenses': total_expenses,
        'total_miles': mileage_stats['total_miles'] or Decimal('0.00'),
        'business_miles': mileage_stats['total_business_miles'] or Decimal('0.00'),
        'personal_miles': mileage_stats['total_personal_miles'] or Decimal('0.00'),
        'net_earnings': total_income - total_expenses
    }

# Utility function for date ranges
def get_date_range(period_type, date=None):
    if date is None:
        date = timezone.now().date()

    if period_type == 'week':
        start_date = date - timezone.timedelta(days=date.weekday())
        end_date = start_date + timezone.timedelta(days=6)
    elif period_type == 'month':
        start_date = date.replace(day=1)
        next_month = date.replace(day=28) + timezone.timedelta(days=4)
        end_date = next_month - timezone.timedelta(days=next_month.day)
    elif period_type == 'quarter':
        quarter = (date.month - 1) // 3
        start_date = date.replace(month=quarter * 3 + 1, day=1)
        end_date = (start_date.replace(month=quarter * 3 + 3, day=28) +
                   timezone.timedelta(days=4)).replace(day=1) - timezone.timedelta(days=1)

    return start_date, end_date

def clean_return_url(request):
    """
    Cleans and validates the return URL for both local development and production
    """
    return_url = request.META.get('HTTP_REFERER')
    if not return_url:
        return None

    # Parse the URL to get the path
    parsed_url = urlparse(return_url)
    path = parsed_url.path

    # Remove any 'JW/' prefix if it exists
    if path.startswith('/JW/'):
        path = path[3:]  # Remove the 'JW' prefix
    elif not path.startswith('/'):
        path = '/' + path

    # Build the cleaned URL using the current request's scheme and host
    cleaned_url = request.build_absolute_uri(path)
    base_url = request.build_absolute_uri('/')[:-1]

    # Verify the URL is from our site
    if cleaned_url.startswith(base_url):
        return cleaned_url
    return None

# Lesson Views
@login_required
def lesson_list(request):
    # Optimize the query with select_related and prefetch_related
    lessons = AppJwLesson.objects.select_related('student').prefetch_related(
        'block_booking_usage__block_booking'
    ).all().order_by('-date')

    # Get all failed tests in one query
    failed_test_lesson_ids = set(
        AppJwFailedTest.objects.values_list('lesson_id', flat=True)
    )

    # Get all students with passed status and test dates in one query
    passed_students = {
        student.student_name: student.test_past
        for student in AppJwStudent.objects.filter(active='Passed').only('student_name', 'test_past')
    }

    # Add test status information to each lesson
    for lesson in lessons:
        lesson.test_status = None  # Default to no test status

        # Check if this lesson has a failed test record
        if lesson.id in failed_test_lesson_ids:
            lesson.test_status = 'failed'
            continue

        # Check if this lesson is a passed test
        if (lesson.student_name in passed_students and
            passed_students[lesson.student_name] == lesson.date):
            lesson.test_status = 'passed'

    # Calculate lesson statistics
    total_hours = sum(lesson.lesson_hours or 0 for lesson in lessons)
    total_income = sum(lesson.amount or 0 for lesson in lessons)
    total_lessons = lessons.count()  # Get total number of lessons
    avg_price_per_hour = Decimal(total_income) / Decimal(total_hours) if total_hours > 0 else Decimal('0.00')
    avg_price_per_lesson = Decimal(total_income) / Decimal(total_lessons) if total_lessons > 0 else Decimal('0.00')  # Calculate average price per lesson

    # Calculate time-based averages for lessons and hours
    from datetime import datetime, timedelta
    from django.utils import timezone
    import calendar

    now = timezone.now().date()

    # Get earliest lesson date to calculate time periods
    earliest_lesson = lessons.last()
    if earliest_lesson:
        days_since_start = (now - earliest_lesson.date).days + 1
        weeks_since_start = Decimal(str(days_since_start)) / Decimal('7')
        months_since_start = Decimal(str(days_since_start)) / Decimal('30.44')  # Average days per month
        quarters_since_start = Decimal(str(days_since_start)) / Decimal('91.31')  # Average days per quarter
    else:
        weeks_since_start = months_since_start = quarters_since_start = Decimal('1')

    # All-time lesson averages
    total_lessons_decimal = Decimal(str(total_lessons))
    total_hours_decimal = Decimal(str(total_hours))
    avg_lessons_per_week_alltime = total_lessons_decimal / weeks_since_start if weeks_since_start > 0 else Decimal('0')
    avg_lessons_per_month_alltime = total_lessons_decimal / months_since_start if months_since_start > 0 else Decimal('0')
    avg_lessons_per_quarter_alltime = total_lessons_decimal / quarters_since_start if quarters_since_start > 0 else Decimal('0')

    # All-time hours averages
    avg_hours_per_week_alltime = total_hours_decimal / weeks_since_start if weeks_since_start > 0 else Decimal('0')
    avg_hours_per_month_alltime = total_hours_decimal / months_since_start if months_since_start > 0 else Decimal('0')
    avg_hours_per_quarter_alltime = total_hours_decimal / quarters_since_start if quarters_since_start > 0 else Decimal('0')

    # Calculate current period statistics
    # Current week (Monday to Sunday)
    today = now
    days_since_monday = today.weekday()
    week_start = today - timedelta(days=days_since_monday)
    week_end = week_start + timedelta(days=6)

    # Current month
    month_start = today.replace(day=1)
    month_end = today.replace(day=calendar.monthrange(today.year, today.month)[1])

    # Current quarter
    quarter = (today.month - 1) // 3 + 1
    quarter_start_month = (quarter - 1) * 3 + 1
    quarter_start = today.replace(month=quarter_start_month, day=1)
    if quarter == 4:
        quarter_end = today.replace(month=12, day=31)
    else:
        next_quarter_start_month = quarter * 3 + 1
        quarter_end = today.replace(month=next_quarter_start_month, day=1) - timedelta(days=1)

    # Current week statistics
    current_week_lessons = lessons.filter(date__range=[week_start, week_end])
    current_week_lessons_count = current_week_lessons.count()
    current_week_hours = sum(lesson.lesson_hours or 0 for lesson in current_week_lessons)

    # Current month statistics
    current_month_lessons = lessons.filter(date__range=[month_start, month_end])
    current_month_lessons_count = current_month_lessons.count()
    current_month_hours = sum(lesson.lesson_hours or 0 for lesson in current_month_lessons)

    # Current quarter statistics
    current_quarter_lessons = lessons.filter(date__range=[quarter_start, quarter_end])
    current_quarter_lessons_count = current_quarter_lessons.count()
    current_quarter_hours = sum(lesson.lesson_hours or 0 for lesson in current_quarter_lessons)

    return render(request, 'JW/lesson_list.html', {
        'lessons': lessons,
        'total_hours': total_hours,
        'total_income': total_income,
        'avg_price_per_hour': avg_price_per_hour,
        'avg_price_per_lesson': avg_price_per_lesson,  # Add to context
        'total_lessons': total_lessons,  # Add to context
        # All-time lesson averages
        'avg_lessons_per_week_alltime': avg_lessons_per_week_alltime,
        'avg_lessons_per_month_alltime': avg_lessons_per_month_alltime,
        'avg_lessons_per_quarter_alltime': avg_lessons_per_quarter_alltime,
        # All-time hours averages
        'avg_hours_per_week_alltime': avg_hours_per_week_alltime,
        'avg_hours_per_month_alltime': avg_hours_per_month_alltime,
        'avg_hours_per_quarter_alltime': avg_hours_per_quarter_alltime,
        # Current period statistics
        'current_week_lessons_count': current_week_lessons_count,
        'current_week_hours': current_week_hours,
        'current_month_lessons_count': current_month_lessons_count,
        'current_month_hours': current_month_hours,
        'current_quarter_lessons_count': current_quarter_lessons_count,
        'current_quarter_hours': current_quarter_hours,
    })

def handle_test_result(request, lesson, action):
    """Helper function to handle test pass/fail actions"""
    try:
        # Get the student without select_for_update first to see current state
        student = AppJwStudent.objects.get(student_name=lesson.student_name)

        # Get test centre if provided
        test_centre = None
        test_centre_id = request.POST.get('test_centre_id')
        if test_centre_id:
            try:
                test_centre = AppJwTestCentre.objects.get(id=test_centre_id, user=request.user)
            except AppJwTestCentre.DoesNotExist:
                messages.warning(request, 'Invalid test centre selected. Test recorded without test centre.')

        if action == 'passed':
            # Direct update approach
            student.active = 'Passed'
            student.test_past = lesson.date
            if test_centre:
                student.test_centre = test_centre
            student.save()

            # Verify the update
            student.refresh_from_db()

            if student.active != 'Passed':
                # Try alternative update method
                update_data = {
                    'active': 'Passed',
                    'test_past': lesson.date
                }
                if test_centre:
                    update_data['test_centre'] = test_centre
                AppJwStudent.objects.filter(id=student.id).update(**update_data)
                student.refresh_from_db()

            test_centre_msg = f' at {test_centre.name}' if test_centre else ''
            messages.success(
                request,
                f'Student {student.student_name} marked as passed{test_centre_msg}! Current status: {student.active}'
            )

        elif action == 'failed':
            failed_test = AppJwFailedTest.objects.create(
                student_name=student.student_name,
                test_date=lesson.date,
                lesson=lesson,
                test_centre=test_centre
            )
            test_centre_msg = f' at {test_centre.name}' if test_centre else ''
            messages.info(request, f'Failed test recorded for {student.student_name}{test_centre_msg}')

    except AppJwStudent.DoesNotExist:
        messages.error(request, f'Student {lesson.student_name} not found')
    except Exception as e:
        # Consider more specific exception handling or logging
        messages.error(request, f'Error processing test result: {str(e)}')
        raise

@login_required
@handle_database_errors
@handle_form_errors
@handle_block_booking_errors
def lesson_create(request):
    return_url = request.GET.get('return_url') or request.POST.get('return_url') or reverse('lesson_list')

    try:
        if request.method == 'POST':
            form = LessonForm(request.POST)
            if form.is_valid():
                lesson = form.save(commit=False)
                lesson.user = request.user

                # Get the student instance
                try:
                    with transaction.atomic():
                        student = AppJwStudent.objects.select_for_update().get(
                            student_name=lesson.student_name,
                            active='Yes'
                        )
                        lesson.student = student
                        lesson.save() # Lesson is saved

                        # Get user settings for block booking overdraft
                        from .models import AppJwUserSettings
                        try:
                            user_settings = AppJwUserSettings.objects.get(user=request.user)
                            allow_overdraft = user_settings.allow_block_booking_overdraft
                        except AppJwUserSettings.DoesNotExist:
                            allow_overdraft = False

                        # Check if block bookings are disabled for this student
                        if not student.block_booking_disabled:
                            # Check for active block bookings with available credit
                            active_block_booking = None
                            potential_bookings = AppJwBlockBooking.objects.filter(
                                student=student,
                                active=True
                            ).order_by('-date_created')

                            # Find the booking with the most available credit
                            max_credit = 0
                            for booking in potential_bookings:
                                if booking.total_value_remaining > max_credit:
                                    max_credit = booking.total_value_remaining
                                    active_block_booking = booking

                            # If there's an active block booking, attempt to add usage
                            if active_block_booking and lesson.lesson_hours > 0:
                                # Use the new add_usage method
                                success = active_block_booking.add_usage(lesson.lesson_hours, lesson.price_per_hour)

                                if not success:
                                    # Not enough credit - check if overdraft is allowed
                                    if not allow_overdraft:
                                        # Delete the lesson and show error message
                                        lesson.delete()
                                        active_block_booking.refresh_from_db() # Get updated remaining credit

                                        # Calculate remaining credit in both monetary and lesson terms
                                        remaining_value = active_block_booking.total_value_remaining
                                        lesson_cost = lesson.lesson_hours * lesson.price_per_hour

                                        if active_block_booking.calculation_method == 'new':
                                            # For new method, show monetary value
                                            messages.error(
                                                request,
                                                f'There is only £{remaining_value:.2f} credit remaining for this student\'s block booking. '
                                                f'This lesson costs £{lesson_cost:.2f} ({lesson.lesson_hours} hours × £{lesson.price_per_hour}/hour). '
                                                f'Please reduce the lesson hours, add a new block booking payment, or enable overdraft in My Data.'
                                            )
                                        else:
                                            # For legacy method, show lesson hours
                                            remaining_hours = active_block_booking.effective_lessons_remaining
                                            messages.error(
                                                request,
                                                f'There is only {remaining_hours:.1f} hours credit remaining for this student\'s block booking. '
                                                f'If you want to record a lesson of more than this, please log the '
                                                f'remaining hours in one lesson and add the remaining lesson hours as a separate lesson, '
                                                f'add a new block booking payment, or enable overdraft in My Data.'
                                            )
                                        return redirect(f"{reverse('lesson_create')}?student_name={lesson.student_name}")
                                    else:
                                        # Overdraft allowed - show warning but continue
                                        remaining_value = active_block_booking.total_value_remaining
                                        lesson_cost = lesson.lesson_hours * lesson.price_per_hour
                                        overdraft_amount = lesson_cost - remaining_value

                                        messages.warning(
                                            request,
                                            f'Lesson recorded with overdraft: £{overdraft_amount:.2f} over available credit. '
                                            f'Student now has negative balance on block booking.'
                                        )

                                # Create usage record only if add_usage was successful
                                AppJwBlockBookingUsage.objects.create(
                                    block_booking=active_block_booking,
                                    lesson=lesson,
                                    lessons_used=lesson.lesson_hours,
                                    date_used=lesson.date
                                )

                    messages.success(request, 'Lesson created successfully.')

                    # Log the lesson creation
                    log_user_action(
                        action='lesson_created',
                        user_id=request.user.id,
                        details={
                            'lesson_id': lesson.id,
                            'student_name': lesson.student_name,
                            'lesson_hours': str(lesson.lesson_hours),
                            'date': str(lesson.date)
                        }
                    )

                    # Handle test pass/fail actions during creation
                    action = request.POST.get('action')
                    if action in ['passed', 'failed']:
                        handle_test_result(request, lesson, action)
                        # After handling pass/fail, redirect to the return_url
                        return redirect(return_url)
                    elif action == 'save_and_add':
                        # If specifically 'save_and_add', redirect to create again
                        return redirect(f"{reverse('lesson_create')}?return_url={return_url}")
                    else:
                        # Default redirect for simple save
                        return redirect(return_url)

                except AppJwStudent.DoesNotExist:
                    messages.error(
                        request,
                        f'Student "{lesson.student_name}" is not active or does not exist.'
                    )
                    return redirect('student_create')
        else:
            initial_data = {}
            if student_name := request.GET.get('student_name'):
                initial_data['student_name'] = student_name
            form = LessonForm(initial=initial_data)

        active_students = AppJwStudent.objects.filter(active='Yes')\
            .values_list('student_name', flat=True)\
            .order_by('student_name')

        return render(request, 'JW/lesson_form.html', {
            'form': form,
            'action': 'Create',
            'existing_students': active_students,
            'return_url': return_url
        })

    except Exception as e:
        messages.error(request, f'An error occurred: {str(e)}')
        return redirect(return_url)

@login_required
@handle_database_errors
@handle_form_errors
@handle_block_booking_errors
def lesson_update(request, pk):
    lesson = get_object_or_404(AppJwLesson, pk=pk)
    if request.method == 'POST':
        action = request.POST.get('action')

        if action in ['passed', 'failed']:
            post_data = request.POST.copy()
            post_data['lesson_hours'] = '0'
            post_data['price_per_hour'] = '0'
            form = LessonForm(post_data, instance=lesson)
        else:
            form = LessonForm(request.POST, instance=lesson)

        if form.is_valid():
            try:
                with transaction.atomic():
                    # Get original lesson hours before saving
                    original_lesson = AppJwLesson.objects.select_for_update().get(pk=lesson.pk)
                    original_hours = original_lesson.lesson_hours

                    # Save the updated lesson
                    lesson = form.save()

                    # Handle block booking usage if lesson hours changed
                    if original_hours != lesson.lesson_hours:
                        # Find existing usage record for this lesson
                        existing_usage = AppJwBlockBookingUsage.objects.filter(lesson=lesson).first()

                        if existing_usage:
                            block_booking = existing_usage.block_booking
                            # Remove original usage first
                            block_booking.remove_usage(original_hours, original_lesson.price_per_hour)
                            existing_usage.delete() # Delete old usage record

                        if lesson.lesson_hours > 0:
                            # Now, try to add the new usage
                            student = lesson.student
                            active_block_booking = None
                            potential_bookings = AppJwBlockBooking.objects.filter(
                                student=student,
                                active=True
                            ).order_by('-date_created')

                            # Find the booking with the most available credit
                            max_credit = 0
                            for booking in potential_bookings:
                                if booking.total_value_remaining > max_credit:
                                    max_credit = booking.total_value_remaining
                                    active_block_booking = booking

                            if active_block_booking:
                                success = active_block_booking.add_usage(lesson.lesson_hours, lesson.price_per_hour)
                                if not success:
                                    # Not enough credit for the new hours, revert lesson and show error
                                    lesson.lesson_hours = original_hours
                                    lesson.save()

                                    # Calculate remaining credit for better error message
                                    remaining_value = active_block_booking.total_value_remaining
                                    lesson_cost = lesson.lesson_hours * lesson.price_per_hour

                                    if active_block_booking.calculation_method == 'new':
                                        messages.error(
                                            request,
                                            f'Not enough credit in block booking. Available: £{remaining_value:.2f}, '
                                            f'Required: £{lesson_cost:.2f}. Reverted to original {original_hours} hours.'
                                        )
                                    else:
                                        remaining_hours = active_block_booking.effective_lessons_remaining
                                        messages.error(
                                            request,
                                            f'Not enough credit in block booking for {lesson.lesson_hours} hours. '
                                            f'Available: {remaining_hours:.1f} hours. Reverted to original {original_hours} hours.'
                                        )
                                    return redirect('lesson_list')
                                
                                # Create new usage record
                                AppJwBlockBookingUsage.objects.create(
                                    block_booking=active_block_booking,
                                    lesson=lesson,
                                    lessons_used=lesson.lesson_hours,
                                    date_used=lesson.date
                                )
                            else:
                                # No active block booking, but lesson hours are > 0, so this is a paid lesson
                                # No block booking update needed, but inform user if they expected one
                                messages.info(request, "No active block booking found for this student to apply lesson hours.")
                    elif lesson.lesson_hours == 0 and original_hours > 0:
                        # If lesson hours changed to 0, remove any associated block booking usage
                        existing_usage = AppJwBlockBookingUsage.objects.filter(lesson=lesson).first()
                        if existing_usage:
                            block_booking = existing_usage.block_booking
                            block_booking.remove_usage(original_hours, original_lesson.price_per_hour)
                            existing_usage.delete()

                if action in ['passed', 'failed']:
                    handle_test_result(request, lesson, action)

                messages.success(request, 'Lesson updated successfully.')
                return redirect('lesson_list')

            except Exception as e:
                # Consider more specific exception handling or logging
                messages.error(request, f'Error updating lesson: {str(e)}')
    else:
        form = LessonForm(instance=lesson)

    return render(request, 'JW/lesson_form.html', {
        'form': form,
        'action': 'Update',
        'lesson': lesson
    })

@login_required
def lesson_delete(request, pk):
    lesson = get_object_or_404(AppJwLesson, pk=pk)

    if request.method == 'POST':
        from django.db import transaction

        student_name = lesson.student_name
        lesson_date = lesson.date

        try:
            with transaction.atomic():
                # First, handle all related records that reference this lesson

                # 1. Delete any failed test records that reference this lesson
                failed_tests = AppJwFailedTest.objects.filter(lesson=lesson)
                failed_test_deleted = False
                if failed_tests.exists():
                    failed_test_count = failed_tests.count()
                    failed_tests.delete()
                    failed_test_deleted = True
                    messages.info(
                        request,
                        f"Removed {failed_test_count} failed test record(s) for '{student_name}' on {lesson_date}."
                    )

                # 2. Handle student status reversals for passed test lessons
                passed_test_reverted = False
                try:
                    student = AppJwStudent.objects.get(student_name=student_name)

                    # Check if this lesson marked the student as Passed
                    if student.active == 'Passed' and student.test_past == lesson_date:
                        student.active = 'Yes'
                        student.test_past = None
                        student.test_centre = None  # Also clear test centre
                        student.save()
                        passed_test_reverted = True
                        messages.warning(
                            request,
                            f"Student '{student_name}' status reverted to 'Yes' as the 'Passed' test lesson was deleted."
                        )

                except AppJwStudent.DoesNotExist:
                    # Student might have been deleted separately, ignore error
                    pass

                # 3. Handle block booking usage before deleting the lesson
                block_booking_usage = AppJwBlockBookingUsage.objects.filter(lesson=lesson).first()
                if block_booking_usage:
                    block_booking = block_booking_usage.block_booking
                    # Use the new remove_usage method
                    block_booking.remove_usage(block_booking_usage.lessons_used, lesson.price_per_hour)
                    block_booking_usage.delete()

                # 4. Finally, delete the lesson itself
                lesson.delete()

                # 5. Provide appropriate success message
                if passed_test_reverted:
                    messages.success(request, f'Lesson deleted successfully. Student status reverted to "Yes".')
                elif failed_test_deleted:
                    messages.success(request, 'Lesson deleted successfully. Failed test record removed.')
                else:
                    messages.success(request, 'Lesson deleted successfully.')

        except Exception as e:
            messages.error(request, f"Error deleting lesson: {str(e)}")
            return redirect('lesson_list')

        return redirect('lesson_list')

    # Prepare details for confirmation page (GET request)
    record_details = {
        'Date': lesson.date,
        'Day': lesson.day_of_week,
        'Student': lesson.student_name,
        'Hours': lesson.lesson_hours,
        'Price/Hour': lesson.price_per_hour,
        'Amount': lesson.amount,
        'Notes': lesson.notes
    }

    # Check if this lesson has test implications
    test_implications = []

    # Check for failed test records
    failed_tests = AppJwFailedTest.objects.filter(lesson=lesson)
    if failed_tests.exists():
        test_implications.append(f"⚠️ This will remove {failed_tests.count()} failed test record(s)")

    # Check if this is a passed test lesson (only show status reversion for passed tests)
    try:
        student = AppJwStudent.objects.get(student_name=lesson.student_name)
        # Only show status reversion if this lesson is the actual passed test lesson
        if student.active == 'Passed' and student.test_past and student.test_past == lesson.date:
            test_implications.append(f"⚠️ Student status will revert from 'Passed' to 'Yes'")
            test_implications.append(f"⚠️ Test pass date will be cleared")
            if student.test_centre:
                test_implications.append(f"⚠️ Test centre ({student.test_centre.name}) will be cleared")
    except AppJwStudent.DoesNotExist:
        pass

    context = {
        'record_details': record_details,
        'test_implications': test_implications,
        'cancel_url': reverse('lesson_list')
    }
    return render(request, 'JW/confirm_delete.html', context)

@login_required
def student_lessons(request, student_name):
    # Get all lessons for the student, ordered by date
    lessons = AppJwLesson.objects.filter(
        student_name=student_name
    ).order_by('-date')

    # Add test status information to each lesson
    for lesson in lessons:
        lesson.test_status = None  # Default to no test status

        # First, check if this specific lesson has a failed test record
        failed_test = AppJwFailedTest.objects.filter(lesson=lesson).first()
        if failed_test:
            lesson.test_status = 'failed'
            continue  # This lesson is definitely a failed test

        # Then check if this specific lesson is a passed test
        try:
            student = AppJwStudent.objects.get(student_name=lesson.student_name)
            # Only mark as passed if this exact lesson date matches the test_past date
            # AND the student is currently marked as passed
            if student.active == 'Passed' and student.test_past and student.test_past == lesson.date:
                lesson.test_status = 'passed'
        except AppJwStudent.DoesNotExist:
            pass

    # Calculate summary statistics
    summary = lessons.aggregate(
        total_hours=Sum('lesson_hours'),
        total_amount=Sum(F('lesson_hours') * F('price_per_hour'))
    )

    context = {
        'student_name': student_name,
        'lessons': lessons,
        'total_hours': summary['total_hours'] or 0,
        'total_amount': summary['total_amount'] or 0,
        'filtered_view': True  # Add this flag
    }

    return render(request, 'JW/student_lessons.html', context)

@login_required
def tests_list(request):
    """View to display all passed and failed tests"""
    # Get all passed tests (students with active='Passed') - include those without test_past date
    passed_tests = AppJwStudent.objects.filter(active='Passed').order_by('-test_past')

    # Get all failed tests (only those with valid lesson references)
    failed_tests = AppJwFailedTest.objects.select_related('lesson', 'test_centre').filter(
        lesson__isnull=False  # Only show failed tests that still have associated lessons
    ).order_by('-test_date')

    # Calculate test centre statistics
    test_centre_stats = {}

    # Get all test centres for the user
    test_centres = AppJwTestCentre.objects.filter(user=request.user, active=True).order_by('name')

    for centre in test_centres:
        # Count passed tests for this centre
        passed_count = passed_tests.filter(test_centre=centre).count()

        # Count failed tests for this centre
        failed_count = failed_tests.filter(test_centre=centre).count()

        test_centre_stats[centre.name] = {
            'centre': centre,
            'passed_count': passed_count,
            'failed_count': failed_count,
            'total_count': passed_count + failed_count
        }

    # Also handle tests without test centre specified
    passed_no_centre = passed_tests.filter(test_centre__isnull=True).count()
    failed_no_centre = failed_tests.filter(test_centre__isnull=True).count()

    if passed_no_centre > 0 or failed_no_centre > 0:
        test_centre_stats['Not Specified'] = {
            'centre': None,
            'passed_count': passed_no_centre,
            'failed_count': failed_no_centre,
            'total_count': passed_no_centre + failed_no_centre
        }

    context = {
        'passed_tests': passed_tests,
        'failed_tests': failed_tests,
        'test_centre_stats': test_centre_stats,
    }

    return render(request, 'JW/tests_list.html', context)

@login_required
def test_edit(request, pk):
    """View to edit test centre for a test record"""
    # Determine if this is a passed or failed test
    failed_test = None
    passed_student = None

    try:
        failed_test = AppJwFailedTest.objects.get(pk=pk)
    except AppJwFailedTest.DoesNotExist:
        try:
            passed_student = AppJwStudent.objects.get(pk=pk, active='Passed')
        except AppJwStudent.DoesNotExist:
            messages.error(request, 'Test record not found.')
            return redirect('tests_list')

    if request.method == 'POST':
        test_centre_id = request.POST.get('test_centre')
        test_date = request.POST.get('test_date')

        if test_centre_id:
            try:
                test_centre = AppJwTestCentre.objects.get(id=test_centre_id, user=request.user)

                if failed_test:
                    failed_test.test_centre = test_centre
                    if test_date:
                        from datetime import datetime
                        failed_test.test_date = datetime.strptime(test_date, '%Y-%m-%d').date()
                    failed_test.save()
                    messages.success(request, f'Test details updated for failed test.')
                elif passed_student:
                    passed_student.test_centre = test_centre
                    if test_date:
                        from datetime import datetime
                        passed_student.test_past = datetime.strptime(test_date, '%Y-%m-%d').date()
                    passed_student.save()
                    messages.success(request, f'Test details updated for passed test.')

                return redirect('tests_list')
            except AppJwTestCentre.DoesNotExist:
                messages.error(request, 'Invalid test centre selected.')
            except ValueError:
                messages.error(request, 'Invalid date format.')
        else:
            messages.error(request, 'Please select a test centre.')

    # Get available test centres
    test_centres = AppJwTestCentre.objects.filter(user=request.user, active=True).order_by('name')

    context = {
        'failed_test': failed_test,
        'passed_student': passed_student,
        'test_centres': test_centres,
    }

    return render(request, 'JW/test_edit.html', context)

@login_required
def student_list(request):
    # Get all stats first before applying filters
    # --- Student Statistics ---
    total_students = AppJwStudent.objects.count()
    active_students_count = AppJwStudent.objects.filter(active='Yes').count()
    inactive_students_count = AppJwStudent.objects.filter(active='No').count()
    passed_students_count = AppJwStudent.objects.filter(active='Passed').count()

    # Calculate average lessons and hours for passed students more efficiently
    avg_lessons_to_pass = Decimal('0.0')
    avg_hours_to_pass = Decimal('0.0')

    # Calculate averages for all students marked as Passed
    if passed_students_count > 0:
        # Aggregate stats directly from lessons linked to passed students
        passed_lesson_stats = AppJwLesson.objects.filter(
            student__active='Passed'  # Filter lessons belonging to students currently marked as Passed
        ).aggregate(
            total_lessons=Count('id'),      # Count all lessons for these students
            total_hours=Sum('lesson_hours') # Sum all hours for these students
        )

        total_lessons_for_passed = passed_lesson_stats.get('total_lessons') or 0
        total_hours_for_passed = passed_lesson_stats.get('total_hours') or Decimal('0.0')

        # Calculate averages using the count of passed students
        avg_lessons_to_pass = round(Decimal(total_lessons_for_passed) / Decimal(passed_students_count), 1)
        avg_hours_to_pass = round(Decimal(total_hours_for_passed) / Decimal(passed_students_count), 1)

    # --- Filtering and Sorting Logic ---
    # Get filter parameters
    filter_status = request.GET.get('status', '')
    filter_gender = request.GET.get('gender', '')
    filter_age_range = request.GET.get('age_range', '')
    filter_area = request.GET.get('area', '')

    # Get sorting parameters
    sort_by = request.GET.get('sort', 'student_name')  # Default sort by name
    order = request.GET.get('order', 'asc')  # Default ascending order

    # Start with all students
    students = AppJwStudent.objects.annotate(
        lesson_count=Count('lessons'),
        last_lesson_date=Subquery(
            AppJwLesson.objects.filter(
                student_id=OuterRef('id')
            ).order_by('-date').values('date')[:1]
        ),
        total_hours=Sum('lessons__lesson_hours'),
        total_amount=Sum(F('lessons__lesson_hours') * F('lessons__price_per_hour')),
        has_block_booking=Exists(
            AppJwBlockBooking.objects.filter(student_id=OuterRef('id'))
        )
    )

    # Apply filters
    if filter_status and filter_status != 'all':
        if filter_status == 'active':
            students = students.filter(active='Yes')
        elif filter_status == 'inactive':
            students = students.filter(active='No')
        elif filter_status == 'passed':
            students = students.filter(active='Passed')

    if filter_gender:
        if filter_gender == 'blank':
            students = students.filter(Q(gender__isnull=True) | Q(gender=''))
        else:
            students = students.filter(gender=filter_gender)

    if filter_area:
        if filter_area == 'blank':
            students = students.filter(Q(area__isnull=True) | Q(area=''))
        else:
            students = students.filter(area=filter_area)

    # Get unique areas from students, excluding empty or null values
    areas = AppJwStudent.objects.exclude(
        Q(area__isnull=True) | Q(area='')
    ).values_list('area', flat=True).distinct().order_by('area')

    # --- Sorting Logic ---
    # Define valid sort fields and their corresponding database fields
    valid_sorts = {
        'name': 'student_name',
        'lessons': 'lesson_count',
        'hours': 'total_hours',
        'revenue': 'total_amount',
        'block_booking': 'has_block_booking',
        'last_lesson': 'last_lesson_date',
        'status': 'active'
    }

    # Validate sort parameter
    if sort_by not in valid_sorts:
        sort_by = 'name'  # Default to name if invalid

    # Get the database field name
    db_field = valid_sorts[sort_by]

    # Apply sorting
    if order == 'desc':
        # For descending order, add minus prefix
        # Handle null values properly for certain fields
        if sort_by in ['hours', 'revenue']:
            # For numeric fields, treat null as 0 for sorting
            students = students.order_by(F(db_field).desc(nulls_last=True))
        elif sort_by == 'last_lesson':
            # For date fields, put null dates last in descending order
            students = students.order_by(F(db_field).desc(nulls_last=True))
        else:
            students = students.order_by(f'-{db_field}')
    else:
        # Ascending order
        if sort_by in ['hours', 'revenue']:
            # For numeric fields, treat null as 0 for sorting
            students = students.order_by(F(db_field).asc(nulls_first=True))
        elif sort_by == 'last_lesson':
            # For date fields, put null dates last in ascending order
            students = students.order_by(F(db_field).asc(nulls_last=True))
        else:
            students = students.order_by(db_field)

    return render(request, 'JW/student_list.html', {
        'students': students,
        'total_students': total_students,
        'active_students': active_students_count,      # Use count variable
        'inactive_students': inactive_students_count,  # Use count variable
        'passed_students': passed_students_count,      # Use count variable
        'avg_lessons_to_pass': avg_lessons_to_pass,
        'avg_hours_to_pass': avg_hours_to_pass,
        'current_filter': filter_status,
        'current_gender': filter_gender,
        'current_area': filter_area,
        'current_age_range': filter_age_range,
        'current_sort': sort_by,
        'current_order': order,
        'areas': areas  # Add this line to pass the areas to the template
    })

@login_required
def student_detail(request, pk):
    student = get_object_or_404(AppJwStudent, pk=pk)

    # Get student's lesson statistics
    student_stats = AppJwStudent.objects.filter(pk=pk).annotate(
        lesson_count=Count('lessons', distinct=True),
        total_hours=Coalesce(Sum('lessons__lesson_hours', output_field=FloatField()), Value(0, output_field=FloatField())),
        total_amount=Coalesce(
            Sum(
                F('lessons__lesson_hours') * F('lessons__price_per_hour'),
                output_field=FloatField()
            ),
            Value(0, output_field=FloatField())
        ),
        last_lesson=Max('lessons__date')
    ).first()

    # Handle case where student has no lessons
    if student_stats:
        student.lesson_count = student_stats.lesson_count or 0
        student.total_hours = student_stats.total_hours or 0
        student.total_amount = student_stats.total_amount or 0
        student.last_lesson = student_stats.last_lesson
    else:
        student.lesson_count = 0
        student.total_hours = 0
        student.total_amount = 0
        student.last_lesson = None

    # Get block booking information with total usage
    block_bookings = AppJwBlockBooking.objects.filter(student=student).order_by('-date_created')

    # Get block booking usage history
    block_booking_usage = AppJwBlockBookingUsage.objects.filter(
        block_booking__student=student
    ).select_related('block_booking', 'lesson').order_by('-date_used')

    return render(request, 'JW/student_detail.html', {
        'student': student,
        'block_bookings': block_bookings,
        'block_booking_usage': block_booking_usage
    })

@login_required
@handle_database_errors
@handle_form_errors
@handle_block_booking_errors
def student_create(request):
    # Get return_url from GET parameters or POST data
    return_url = request.GET.get('return_url') or request.POST.get('return_url') or reverse('student_list')

    if request.method == 'POST':
        form = StudentForm(request.POST)
        if form.is_valid():
            try:
                # Save the student first
                student = form.save()

                # Check if block booking data was submitted
                has_block_booking = request.POST.get('has_block_booking') == 'true'

                if has_block_booking:
                    try:
                        # Get block booking data from the form
                        block_booking_form = BlockBookingForm(request.POST)
                        if block_booking_form.is_valid():
                            block_booking = block_booking_form.save(commit=False)
                            block_booking.student = student
                            block_booking.lessons_used = 0
                            block_booking.active = True
                            block_booking.save()
                            messages.success(request, 'Student and block booking created successfully.')
                        else:
                            # If block booking form is invalid, show errors but still create student
                            for field, errors in block_booking_form.errors.items():
                                for error in errors:
                                    messages.error(request, f"Block Booking - {field}: {error}")
                            messages.warning(request, 'Student created but block booking data was incomplete or invalid.')
                    except Exception as e:
                        messages.error(request, f'Student created but error saving block booking: {str(e)}')
                else:
                    messages.success(request, 'Student created successfully.')

                # Check if user wants to add another
                if request.POST.get('action') == 'save_and_add':
                    # When adding another, preserve the original return_url
                    return redirect(f"{reverse('student_create')}?return_url={return_url}")

                # Otherwise return to the original return_url
                return redirect(return_url)

            except IntegrityError as e:
                # Handle database constraint violations gracefully
                error_message = str(e)
                if 'mobile_number_check' in error_message:
                    form.add_error('mobile_number',
                        'Invalid mobile number format. Please enter a valid UK mobile number (e.g. 07123456789).')
                elif 'unique' in error_message.lower():
                    if 'student_name' in error_message:
                        form.add_error('student_name', 'A student with this name already exists.')
                    else:
                        form.add_error(None, 'This information already exists in the database.')
                else:
                    form.add_error(None, 'There was an error saving the student. Please check your information and try again.')

            except Exception as e:
                # Handle any other unexpected errors
                import traceback
                error_details = f'An unexpected error occurred: {str(e)}\n\nDetailed error:\n{traceback.format_exc()}'
                form.add_error(None, error_details)
    else:
        initial_data = {}
        # Check for existing remainder balance from inactive block bookings
        student_id = request.GET.get('student_id') # Might be passed if coming from lesson creation
        if student_id:
            try:
                student = AppJwStudent.objects.get(pk=student_id)
                inactive_block_bookings_with_remainder = AppJwBlockBooking.objects.filter(
                    student=student,
                    active=False,
                    remainder_balance__gt=0
                )
                total_remainder_to_carry_over = inactive_block_bookings_with_remainder.aggregate(
                    total=Sum('remainder_balance')
                )['total'] or Decimal('0.00')

                if total_remainder_to_carry_over > 0:
                    initial_data['amount_paid'] = total_remainder_to_carry_over
                    messages.info(request, f"Carried over £{total_remainder_to_carry_over:.2f} remainder from previous block bookings.")
                    # Zero out the remainder on old bookings
                    inactive_block_bookings_with_remainder.update(remainder_balance=0)

            except AppJwStudent.DoesNotExist:
                pass # Student not found, proceed without remainder

        form = StudentForm(initial=initial_data)

    # Get areas directly from the database
    areas = AppJwStudent.objects.exclude(
        Q(area__isnull=True) | Q(area='')
    ).values_list('area', flat=True).distinct().order_by('area')

    # Get today's date for the block booking form
    today_date = timezone.now().date()

    context = {
        'form': form,
        'title': 'Add Student',
        'return_url': return_url,
        'areas': areas,
        'today_date': today_date,
        'has_active_block_booking': False  # New students don't have block bookings yet
    }
    return render(request, 'JW/student_form.html', context)

@login_required
@handle_database_errors
@handle_form_errors
@handle_block_booking_errors
def student_edit(request, pk):
    student = get_object_or_404(AppJwStudent, pk=pk)

    # Get all current filter parameters
    filter_params = {
        'status': request.GET.get('status'),
        'gender': request.GET.get('gender'),
        'age_range': request.GET.get('age_range'),
        'area': request.GET.get('area')
    }
    # Remove None and empty values
    filter_params = {k: v for k, v in filter_params.items() if v}

    # Build the return URL with filters
    base_url = reverse('student_list')
    if filter_params:
        query_string = '&'.join(f'{k}={v}' for k, v in filter_params.items())
        return_url = f'{base_url}?{query_string}'
    else:
        return_url = base_url

    if request.method == 'POST':
        form = StudentForm(request.POST, instance=student)
        if form.is_valid():
            try:
                # Save the student first
                student = form.save()

                # Check if block booking data was submitted
                has_block_booking = request.POST.get('has_block_booking') == 'true'

                if has_block_booking:
                    try:
                        with transaction.atomic():
                            # Check for existing remainder balance from INACTIVE block bookings
                            previous_remainder = AppJwBlockBooking.objects.filter(
                                student=student,
                                active=False,
                                remainder_balance__gt=0
                            ).aggregate(total=Sum('remainder_balance'))['total'] or Decimal('0.00')

                            # Create a mutable copy of POST data
                            post_data = request.POST.copy()

                            if previous_remainder > 0:
                                # Add remainder to the amount_paid for the new block booking
                                current_amount_paid = Decimal(post_data.get('amount_paid', '0.00'))
                                post_data['amount_paid'] = str(current_amount_paid + previous_remainder)
                                messages.info(request, f"Added £{previous_remainder:.2f} remainder from previous block bookings.")

                                # Zero out the remainder on old bookings
                                AppJwBlockBooking.objects.filter(
                                    student=student,
                                    active=False,
                                    remainder_balance__gt=0
                                ).update(remainder_balance=0)

                            # Get block booking data from the form with modified POST data
                            block_booking_form = BlockBookingForm(post_data)
                            if block_booking_form.is_valid():
                                block_booking = block_booking_form.save(commit=False)
                                block_booking.student = student
                                block_booking.lessons_used = 0
                                block_booking.active = True
                                block_booking.save()
                                messages.success(request, 'Student updated and block booking created successfully.')
                            else:
                                # If block booking form is invalid, show errors but still update student
                                for field, errors in block_booking_form.errors.items():
                                    for error in errors:
                                        messages.error(request, f"Block Booking - {field}: {error}")
                                messages.warning(request, 'Student updated but block booking data was incomplete or invalid.')
                    except Exception as e:
                        messages.error(request, f'Student updated but error saving block booking: {str(e)}')
                else:
                    messages.success(request, 'Student updated successfully.')

                return redirect(return_url)

            except IntegrityError as e:
                # Handle database constraint violations gracefully
                error_message = str(e)
                if 'mobile_number_check' in error_message:
                    form.add_error('mobile_number',
                        'Invalid mobile number format. Please enter a valid UK mobile number (e.g. 07123456789).')
                elif 'unique' in error_message.lower():
                    if 'student_name' in error_message:
                        form.add_error('student_name', 'A student with this name already exists.')
                    else:
                        form.add_error(None, 'This information already exists in the database.')
                else:
                    form.add_error(None, 'There was an error saving the student. Please check your information and try again.')

            except Exception as e:
                # Handle any other unexpected errors
                import traceback
                error_details = f'An unexpected error occurred: {str(e)}\n\nDetailed error:\n{traceback.format_exc()}'
                form.add_error(None, error_details)
    else:
        initial_data = {}
        # Check for existing remainder balance from inactive block bookings
        inactive_block_bookings_with_remainder = AppJwBlockBooking.objects.filter(
            student=student,
            active=False,
            remainder_balance__gt=0
        )
        total_remainder_to_carry_over = inactive_block_bookings_with_remainder.aggregate(
            total=Sum('remainder_balance')
        )['total'] or Decimal('0.00')

        if total_remainder_to_carry_over > 0:
            initial_data['amount_paid'] = total_remainder_to_carry_over
            messages.info(request, f"Carried over £{total_remainder_to_carry_over:.2f} remainder from previous block bookings.")
            # Zero out the remainder on old bookings
            inactive_block_bookings_with_remainder.update(remainder_balance=0)

        form = StudentForm(instance=student, initial=initial_data)

    # Get block booking information
    block_bookings = AppJwBlockBooking.objects.filter(student=student).order_by('-date_created')

    # Check if there's an active block booking with remaining credit
    has_active_block_booking = False

    # Calculate total usage for each block booking
    for booking in block_bookings:
        # Get total usage for this booking
        total_usage = AppJwBlockBookingUsage.objects.filter(
            block_booking=booking
        ).aggregate(total=Sum('lessons_used'))['total'] or 0

        # Update the lessons_used field
        booking.lessons_used = total_usage

        # Check if this booking is active and has remaining credit
        if booking.active and booking.lessons_used < booking.total_lessons:
            has_active_block_booking = True

    # Get today's date for the block booking form
    today_date = timezone.now().date()

    return render(request, 'JW/student_form.html', {
        'form': form,
        'title': 'Edit Student',
        'return_url': return_url,
        'student': student,
        'block_bookings': block_bookings,
        'today_date': today_date,
        'has_active_block_booking': has_active_block_booking
    })

@login_required
def student_delete(request, pk):
    student = get_object_or_404(AppJwStudent, pk=pk)
    if request.method == 'POST':
        try:
            # First, check if there are any block bookings for this student
            block_bookings = AppJwBlockBooking.objects.filter(student=student)

            if block_bookings.exists():
                # Get all block booking IDs
                block_booking_ids = block_bookings.values_list('id', flat=True)

                # Delete all block booking usage records first
                AppJwBlockBookingUsage.objects.filter(block_booking_id__in=block_booking_ids).delete()

                # Then delete the block bookings
                block_bookings.delete()

                # Now we can safely delete the student
                student.delete()
                messages.success(request, 'Student and associated block bookings deleted successfully.')
            else:
                # No block bookings, just delete the student
                student.delete()
                messages.success(request, 'Student deleted successfully.')

            return redirect('student_list')
        except Exception as e:
            messages.error(request, f'Error deleting student: {str(e)}')
            return redirect('student_list')

    # Get block booking information for confirmation
    block_bookings = AppJwBlockBooking.objects.filter(student=student).order_by('-date_created')
    has_block_bookings = block_bookings.exists()

    record_details = {
        'Name': student.student_name,
        'Mobile': student.mobile_number,
        'Email': student.email_address,
        'Active': student.active,
        'Notes': student.notes
    }

    if has_block_bookings:
        record_details['Warning'] = f'This student has {block_bookings.count()} block booking(s) that will also be deleted.'

    context = {
        'record_details': record_details,
        'cancel_url': reverse('student_list')
    }
    return render(request, 'JW/confirm_delete.html', context)

@login_required
def get_active_students(request):
    # Modified to only return active students
    students = AppJwStudent.objects.filter(active='Yes').values('student_name')
    return JsonResponse(list(students), safe=False)

@login_required
def get_areas(request):
    # Get unique areas from students, excluding empty or null values
    areas = AppJwStudent.objects.exclude(area__isnull=True)\
                               .exclude(area__exact='')\
                               .values_list('area', flat=True)\
                               .distinct()\
                               .order_by('area')
    return JsonResponse(list(areas), safe=False)

@login_required
@handle_database_errors
@handle_form_errors
@handle_block_booking_errors
def add_block_booking(request):
    """Add a new block booking for a student"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method is allowed'})

    try:
        with transaction.atomic():
            # Get form data
            student_id = request.POST.get('student_id')
            student = get_object_or_404(AppJwStudent, pk=student_id)

            # Lock the student row to prevent race conditions
            student = AppJwStudent.objects.select_for_update().get(pk=student_id)

            # Check for existing remainder balance from all active block bookings
            active_block_bookings_with_remainder = AppJwBlockBooking.objects.filter(
                student=student,
                active=True,
                remainder_balance__gt=0
            )
            total_remainder_to_carry_over = active_block_bookings_with_remainder.aggregate(
                total=Sum('remainder_balance')
            )['total'] or Decimal('0.00')

            # Create a mutable copy of POST data
            post_data = request.POST.copy()

            if total_remainder_to_carry_over > 0:
                # Add remainder to the amount_paid for the new block booking
                current_amount_paid = Decimal(post_data.get('amount_paid', '0.00'))
                post_data['amount_paid'] = str(current_amount_paid + total_remainder_to_carry_over)
                messages.info(request, f"Carried over £{total_remainder_to_carry_over:.2f} remainder from previous block bookings.")
                # Deactivate old bookings since we're carrying over their remainder
                active_block_bookings_with_remainder.update(active=False)

            # Use the BlockBookingForm for validation and saving with modified POST data
            form = BlockBookingForm(post_data)
            if form.is_valid():
                block_booking = form.save(commit=False)
                block_booking.student = student
                block_booking.lessons_used = 0  # Ensure lessons_used is 0 for new bookings
                block_booking.active = True
                block_booking.save()

                return JsonResponse({
                    'success': True,
                    'message': 'Block booking created successfully',
                    'block_booking_id': block_booking.id
                })
            else:
                # If form is invalid, return errors
                errors = {field: error[0] for field, error in form.errors.items()}
                return JsonResponse({'success': False, 'error': 'Validation failed', 'errors': errors})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def get_active_block_bookings(request):
    """Get active block bookings for a student"""
    student_name = request.GET.get('student_name')
    if not student_name:
        return JsonResponse({'success': False, 'error': 'Student name is required'})

    try:
        # Get student
        student = get_object_or_404(AppJwStudent, student_name=student_name)

        # Get active block bookings with available credit
        block_booking = None
        potential_bookings = AppJwBlockBooking.objects.filter(
            student=student,
            active=True
        ).order_by('-date_created')

        # Find the booking with the most available credit
        max_credit = 0
        for booking in potential_bookings:
            if booking.total_value_remaining > max_credit:
                max_credit = booking.total_value_remaining
                block_booking = booking

        if block_booking:
            # Calculate total usage for this booking
            total_usage = AppJwBlockBookingUsage.objects.filter(
                block_booking=block_booking
            ).aggregate(total=Sum('lessons_used'))['total'] or 0

            # Update the lessons_used field
            block_booking.lessons_used = total_usage

            # Return block booking data
            return JsonResponse({
                'success': True,
                'block_booking': {
                    'id': block_booking.id,
                    'date_created': block_booking.date_created,
                    'amount_paid': float(block_booking.amount_paid),
                    'total_lessons': float(block_booking.total_lessons),
                    'lessons_used': float(total_usage),
                    'lessons_remaining': float(block_booking.lessons_remaining),
                    'price_per_lesson': float(block_booking.price_per_lesson),
                    'price_per_lesson_fixed': float(block_booking.price_per_lesson_fixed) if block_booking.price_per_lesson_fixed else None,
                    'remainder_balance': float(block_booking.remainder_balance),
                    'calculation_method': block_booking.calculation_method,
                    'effective_lessons_remaining': float(block_booking.effective_lessons_remaining),
                    'total_value_remaining': float(block_booking.total_value_remaining)
                }
            })
        else:
            return JsonResponse({'success': True, 'block_booking': None})

    except AppJwStudent.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Student not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def get_student_remainder(request):
    """Get the total remainder balance from all active block bookings for the student"""
    student_name = request.GET.get('student_name')
    if not student_name:
        return JsonResponse({'success': False, 'error': 'Student name is required'})

    try:
        # Get student
        student = get_object_or_404(AppJwStudent, student_name=student_name)

        # Get total remainder from all active block bookings
        total_remainder = AppJwBlockBooking.objects.filter(
            student=student,
            active=True
        ).aggregate(total=Sum('remainder_balance'))['total'] or Decimal('0.00')

        return JsonResponse({
            'success': True,
            'remainder': float(total_remainder)
        })

    except AppJwStudent.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Student not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def get_test_centres(request):
    """Get test centres for the current user"""
    test_centres = AppJwTestCentre.objects.filter(user=request.user, active=True).order_by('name')

    test_centres_data = [
        {
            'id': tc.id,
            'name': tc.name
        }
        for tc in test_centres
    ]

    return JsonResponse({
        'success': True,
        'test_centres': test_centres_data
    })

@login_required
def refund_remainder(request):
    """Refund the remainder balance of a block booking to the student"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method is allowed'})

    try:
        import json
        data = json.loads(request.body)
        booking_id = data.get('booking_id')

        if not booking_id:
            return JsonResponse({'success': False, 'error': 'Booking ID is required'})

        # Get the block booking
        booking = get_object_or_404(AppJwBlockBooking, pk=booking_id)

        if booking.remainder_balance <= 0:
            return JsonResponse({'success': False, 'error': 'No remainder balance to refund'})

        # Store the refunded amount for the response
        refunded_amount = booking.remainder_balance

        # Set remainder balance to 0
        booking.remainder_balance = Decimal('0.00')
        booking.save()

        return JsonResponse({
            'success': True,
            'message': f'Successfully refunded £{refunded_amount:.2f} to student',
            'refunded_amount': float(refunded_amount)
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def delete_block_booking(request):
    """Delete a block booking if no lessons have been used"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method is allowed'})

    try:
        import json
        data = json.loads(request.body)
        booking_id = data.get('booking_id')

        if not booking_id:
            return JsonResponse({'success': False, 'error': 'Booking ID is required'})

        # Get the block booking
        booking = get_object_or_404(AppJwBlockBooking, pk=booking_id)

        # Check if any lessons have been used
        if booking.lessons_used > 0:
            return JsonResponse({
                'success': False,
                'error': f'Cannot delete this block booking because {booking.lessons_used} lesson(s) have been deducted from it. Please remove the lessons for this block booking first!'
            })

        # Check if there are any usage records for this booking
        usage_records = AppJwBlockBookingUsage.objects.filter(block_booking=booking)
        if usage_records.exists():
            return JsonResponse({
                'success': False,
                'error': 'Cannot delete this block booking because it has associated lesson records. Please remove the lessons first!'
            })

        # Store booking details for the response
        student_name = booking.student.student_name
        amount_paid = booking.amount_paid

        # Delete the booking
        booking.delete()

        return JsonResponse({
            'success': True,
            'message': f'Block booking for £{amount_paid:.2f} deleted successfully for {student_name}'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def edit_block_booking(request, booking_id):
    """Edit an existing block booking with validation against existing lessons"""
    booking = get_object_or_404(AppJwBlockBooking, pk=booking_id)
    student = booking.student

    # Get all lessons that used this block booking
    related_lessons = AppJwLesson.objects.filter(
        student=student,
        date__gte=booking.date_created
    ).order_by('date')

    # Calculate total usage from lessons for this booking
    total_lesson_usage = AppJwBlockBookingUsage.objects.filter(
        block_booking=booking
    ).aggregate(total=Sum('lessons_used'))['total'] or Decimal('0.00')

    if request.method == 'POST':
        form = BlockBookingForm(request.POST, instance=booking)
        if form.is_valid():
            try:
                # Get the new values from the form
                new_amount_paid = form.cleaned_data['amount_paid']
                new_total_lessons = form.cleaned_data['total_lessons']
                new_price_per_lesson = form.cleaned_data['price_per_lesson_fixed']
                new_calculation_method = form.cleaned_data['calculation_method']

                # Validate against existing lesson usage
                if new_calculation_method == 'new' and new_price_per_lesson:
                    # Calculate what the new lesson allocation would be
                    new_lesson_allocation_value = new_total_lessons * new_price_per_lesson
                    new_remainder = new_amount_paid - new_lesson_allocation_value

                    # Check if current usage would still be valid
                    current_lesson_value_used = booking.lessons_used * new_price_per_lesson

                    # If lessons used exceeds new lesson allocation, check if remainder can cover it
                    if current_lesson_value_used > new_lesson_allocation_value:
                        excess_needed = current_lesson_value_used - new_lesson_allocation_value
                        if excess_needed > new_remainder:
                            # This change would break existing lesson records
                            conflicting_lessons = []
                            running_total = Decimal('0.00')

                            for lesson in related_lessons:
                                lesson_value = lesson.lesson_hours * lesson.price_per_hour
                                running_total += lesson_value
                                if running_total > (new_lesson_allocation_value + new_remainder):
                                    conflicting_lessons.append(lesson)

                            if conflicting_lessons:
                                error_msg = "Cannot save these changes as they would conflict with existing lessons. "
                                error_msg += f"The following lessons would exceed the new booking limits:\n\n"
                                for lesson in conflicting_lessons[:3]:  # Show first 3 conflicting lessons
                                    error_msg += f"• {lesson.date}: {lesson.lesson_hours}h at £{lesson.price_per_hour}/h = £{lesson.amount}\n"
                                if len(conflicting_lessons) > 3:
                                    error_msg += f"... and {len(conflicting_lessons) - 3} more lessons.\n\n"
                                error_msg += "Please delete or modify these lessons before changing the block booking."

                                form.add_error(None, error_msg)
                                raise ValueError("Validation failed")

                # If we get here, the changes are valid
                updated_booking = form.save()
                messages.success(request, 'Block booking updated successfully.')
                return redirect('student_edit', pk=student.pk)

            except ValueError:
                # Validation failed, form errors already added
                pass
            except Exception as e:
                form.add_error(None, f'An error occurred while saving: {str(e)}')
    else:
        form = BlockBookingForm(instance=booking)

    # Calculate some helpful information for the template
    context = {
        'form': form,
        'booking': booking,
        'student': student,
        'related_lessons': related_lessons,
        'total_lesson_usage': total_lesson_usage,
        'title': f'Edit Block Booking - {student.student_name}',
    }

    return render(request, 'JW/edit_block_booking.html', context)

# Business Expense Views
@login_required
def business_expense_list(request):
    from datetime import datetime, timedelta
    from django.utils import timezone
    import calendar

    expenses = AppJwBusinessexpense.objects.all().order_by('-date')
    total_cost = sum(expense.cost or 0 for expense in expenses)
    avg_cost = total_cost / len(expenses) if expenses else 0

    # Get the most common expense type
    expense_types = {}
    for expense in expenses:
        if expense.expense_type:
            expense_types[expense.expense_type] = expense_types.get(expense.expense_type, 0) + expense.cost
    top_expense_type = max(expense_types.items(), key=lambda x: x[1])[0] if expense_types else None

    # Count distinct expense types
    distinct_expense_types = len(expense_types)

    # Calculate time-based averages for expenses
    now = timezone.now().date()

    # Get earliest expense date to calculate time periods
    earliest_expense = expenses.last()
    if earliest_expense:
        days_since_start = (now - earliest_expense.date).days + 1
        weeks_since_start = Decimal(str(days_since_start)) / Decimal('7')
        months_since_start = Decimal(str(days_since_start)) / Decimal('30.44')  # Average days per month
        quarters_since_start = Decimal(str(days_since_start)) / Decimal('91.31')  # Average days per quarter
    else:
        weeks_since_start = months_since_start = quarters_since_start = Decimal('1')

    # All-time expense averages
    total_cost_decimal = Decimal(str(total_cost))
    avg_expenses_per_week_alltime = total_cost_decimal / weeks_since_start if weeks_since_start > 0 else Decimal('0')
    avg_expenses_per_month_alltime = total_cost_decimal / months_since_start if months_since_start > 0 else Decimal('0')
    avg_expenses_per_quarter_alltime = total_cost_decimal / quarters_since_start if quarters_since_start > 0 else Decimal('0')

    # Calculate current period statistics
    # Current week (Monday to Sunday)
    today = now
    days_since_monday = today.weekday()
    week_start = today - timedelta(days=days_since_monday)
    week_end = week_start + timedelta(days=6)

    # Current month
    month_start = today.replace(day=1)
    month_end = today.replace(day=calendar.monthrange(today.year, today.month)[1])

    # Current quarter
    quarter = (today.month - 1) // 3 + 1
    quarter_start_month = (quarter - 1) * 3 + 1
    quarter_start = today.replace(month=quarter_start_month, day=1)
    if quarter == 4:
        quarter_end = today.replace(month=12, day=31)
    else:
        next_quarter_start_month = quarter * 3 + 1
        quarter_end = today.replace(month=next_quarter_start_month, day=1) - timedelta(days=1)

    # Current week statistics
    current_week_expenses = expenses.filter(date__range=[week_start, week_end])
    current_week_count = current_week_expenses.count()
    current_week_total = sum(expense.cost or 0 for expense in current_week_expenses)

    # Current month statistics
    current_month_expenses = expenses.filter(date__range=[month_start, month_end])
    current_month_count = current_month_expenses.count()
    current_month_total = sum(expense.cost or 0 for expense in current_month_expenses)

    # Current quarter statistics
    current_quarter_expenses = expenses.filter(date__range=[quarter_start, quarter_end])
    current_quarter_count = current_quarter_expenses.count()
    current_quarter_total = sum(expense.cost or 0 for expense in current_quarter_expenses)

    return render(request, 'JW/business_expense_list.html', {
        'expenses': expenses,
        'total_cost': total_cost,
        'avg_cost': avg_cost,
        'top_expense_type': top_expense_type,
        'distinct_expense_types': distinct_expense_types,
        # All-time expense averages
        'avg_expenses_per_week_alltime': avg_expenses_per_week_alltime,
        'avg_expenses_per_month_alltime': avg_expenses_per_month_alltime,
        'avg_expenses_per_quarter_alltime': avg_expenses_per_quarter_alltime,
        # Current period statistics
        'current_week_count': current_week_count,
        'current_week_total': current_week_total,
        'current_month_count': current_month_count,
        'current_month_total': current_month_total,
        'current_quarter_count': current_quarter_count,
        'current_quarter_total': current_quarter_total,
    })

@login_required
def business_expense_create(request):
    if request.method == 'POST':
        form = BusinessExpenseForm(request.POST)
        if form.is_valid():
            expense = form.save(commit=False)
            expense.user = request.user
            expense.save()
            messages.success(request, 'Business expense created successfully.')
            return redirect('business_expense_list')
    else:
        form = BusinessExpenseForm()

    return render(request, 'JW/business_expense_form.html', {
        'form': form,
        'action': 'Create',
    })

@login_required
def business_expense_update(request, pk):
    expense = get_object_or_404(AppJwBusinessexpense, pk=pk)
    if request.method == 'POST':
        form = BusinessExpenseForm(request.POST, instance=expense)
        if form.is_valid():
            form.save()
            messages.success(request, 'Business expense updated successfully.')
            return redirect('business_expense_list')
    else:
        form = BusinessExpenseForm(instance=expense)

    return render(request, 'JW/business_expense_form.html', {
        'form': form,
        'action': 'Update',
    })

@login_required
def business_expense_delete(request, pk):
    expense = get_object_or_404(AppJwBusinessexpense, pk=pk)
    if request
        quarter_end = today.replace(month=next_quarter_start_month, day=1) - timedelta(days=1)

    # Current week statistics
    current_week_expenses = expenses.filter(date__range=[week_start, week_end])
    current_week_count = current_week_expenses.count()
    current_week_total = sum(expense.cost or 0 for expense in current_week_expenses)
    current_week_avg = current_week_total / current_week_count if current_week_count > 0 else 0

    # Current month statistics
    current_month_expenses = expenses.filter(date__range=[month_start, month_end])
    current_month_count = current_month_expenses.count()
    current_month_total = sum(expense.cost or 0 for expense in current_month_expenses)
    current_month_avg = current_month_total / current_month_count if current_month_count > 0 else 0

    # Current quarter statistics
    current_quarter_expenses = expenses.filter(date__range=[quarter_start, quarter_end])
    current_quarter_count = current_quarter_expenses.count()
    current_quarter_total = sum(expense.cost or 0 for expense in current_quarter_expenses)
    current_quarter_avg = current_quarter_total / current_quarter_count if current_quarter_count > 0 else 0

    return render(request, 'JW/business_expense_list.html', {
        'expenses': expenses,
        'total_cost': total_cost,
        'avg_cost': avg_cost,
        'top_expense_type': top_expense_type,
        'distinct_expense_types': distinct_expense_types,
        'avg_expenses_per_week_alltime': avg_expenses_per_week_alltime,
        'avg_expenses_per_month_alltime': avg_expenses_per_month_alltime,
        'avg_expenses_per_quarter_alltime': avg_expenses_per_quarter_alltime,
        'current_week_count': current_week_count,
        'current_week_total': current_week_total,
        'current_week_avg': current_week_avg,
        'current_month_count': current_month_count,
        'current_month_total': current_month_total,
        'current_month_avg': current_month_avg,
        'current_quarter_count': current_quarter_count,
        'current_quarter_total': current_quarter_total,
        'current_quarter_avg': current_quarter_avg,
    })

@login_required
def business_expense_create(request):
    if request.method == 'POST':
        form = BusinessExpenseForm(request.POST)
        if form.is_valid():
            expense = form.save(commit=False)
            expense.user = request.user
            expense.save()
            messages.success(request, 'Business expense created successfully.')
            return redirect('business_expense_list')
    else:
        form = BusinessExpenseForm()

    return render(request, 'JW/business_expense_form.html', {
        'form': form,
        'action': 'Create',
    })

@login_required
def business_expense_update(request, pk):
    expense = get_object_or_404(AppJwBusinessexpense, pk=pk)
    if request.method == 'POST':
        form = BusinessExpenseForm(request.POST, instance=expense)
        if form.is_valid():
            form.save()
            messages.success(request, 'Business expense
    current_week_expenses = expenses.filter(date__range=[week_start, week_end])
    current_week_expenses_cost = sum(Decimal(str(expense.cost or 0)) for expense in current_week_expenses)

    # Current month statistics
    current_month_expenses = expenses.filter(date__range=[month_start, month_end])
    current_month_expenses_cost = sum(Decimal(str(expense.cost or 0)) for expense in current_month_expenses)

    # Current quarter statistics
    current_quarter_expenses = expenses.filter(date__range=[quarter_start, quarter_end])
    current_quarter_expenses_cost = sum(Decimal(str(expense.cost or 0)) for expense in current_quarter_expenses)

    return render(request, 'JW/business_expense_list.html', {
        'expenses': expenses,
        'total_cost': total_cost,
        'avg_cost': avg_cost,
        'top_expense_type': top_expense_type,
        'distinct_expense_types': distinct_expense_types,
        # All-time averages
        'avg_expenses_per_week_alltime': avg_expenses_per_week_alltime,
        'avg_expenses_per_month_alltime': avg_expenses_per_month_alltime,
        'avg_expenses_per_quarter_alltime': avg_expenses_per_quarter_alltime,
        # Current period statistics
        'current_week_expenses_cost': current_week_expenses_cost,
        'current_month_expenses_cost': current_month_expenses_cost,
        'current_quarter_expenses_cost': current_quarter_expenses_cost,
    })

def get_expense_types():
    """Helper function to get unique expense types from existing business expenses"""
    return AppJwBusinessexpense.objects.exclude(expense_type__isnull=True)\
                                     .exclude(expense_type__exact='')\
                                     .values_list('expense_type', flat=True)\
                                     .distinct()\
                                     .order_by('expense_type')

@login_required
def business_expense_create(request):
    return_url = request.GET.get('return_url') or request.POST.get('return_url') or reverse('business_expense_list')

    try:
        if request.method == 'POST':
            form = BusinessExpenseForm(request.POST)
            if form.is_valid():
                expense = form.save(commit=False)
                expense.user = request.user
                expense.save()

                messages.success(request, 'Business expense created successfully.')

                if request.POST.get('action') == 'save_and_add':
                    return redirect(f"{reverse('business_expense_create')}?return_url={return_url}")

                return redirect(return_url)
        else:
            initial_data = {}
            if expense_type := request.GET.get('expense_type'):
                initial_data['expense_type'] = expense_type
            form = BusinessExpenseForm(initial=initial_data)

        # Get existing expense types for suggestions
        expense_types = AppJwBusinessexpense.objects.exclude(expense_type__isnull=True)\
            .exclude(expense_type__exact='')\
            .values_list('expense_type', flat=True)\
            .distinct()\
            .order_by('expense_type')

        return render(request, 'JW/business_expense_form.html', {
            'form': form,
            'action': 'Add',
            'return_url': return_url,
            'expense_types': expense_types
        })

    except Exception as e:
        messages.error(request, f'An error occurred: {str(e)}')
        return redirect(return_url)

@login_required
def business_expense_update(request, pk):
    expense = get_object_or_404(AppJwBusinessexpense, pk=pk)
    if request.method == 'POST':
        form = BusinessExpenseForm(request.POST, instance=expense)
        if form.is_valid():
            expense = form.save()
            messages.success(request, 'Business expense updated successfully.')
            return redirect('business_expense_list')
    else:
        form = BusinessExpenseForm(instance=expense)

    context = {
        'form': form,
        'action': 'Update',
    }
    return render(request, 'JW/business_expense_form.html', context)

@login_required
def business_expense_delete(request, pk):
    expense = get_object_or_404(AppJwBusinessexpense, pk=pk)
    if request.method == 'POST':
        expense.delete()
        messages.success(request, 'Business expense deleted successfully.')
        return redirect('business_expense_list')

    record_details = {
        'Date': expense.date,
        'Day': expense.day_of_week,
        'Type': expense.expense_type,
        'Category': expense.category,
        'Cost': expense.cost,
        'Description': expense.description,
        'Notes': expense.notes
    }
    context = {
        'record_details': record_details,
        'cancel_url': reverse('business_expense_list')
    }
    return render(request, 'JW/confirm_delete.html', context)

@login_required
def expense_type_details(request, expense_type):
    expenses = AppJwBusinessexpense.objects.filter(
        expense_type=expense_type
    ).order_by('-date')

    # Calculate summary statistics
    summary = expenses.aggregate(
        total_cost=Sum('cost')  # Changed from 'amount' to 'cost'
    )

    context = {
        'expense_type': expense_type,
        'expenses': expenses,
        'total_cost': summary['total_cost'] or 0,  # Changed from 'total_amount' to 'total_cost'
        'filtered_view': True
    }

    return render(request, 'JW/expense_type_details.html', context)

# Business Mileage Views
@login_required
def business_mileage_list(request):
    mileages = AppJwBusinessmileage.objects.all().order_by('-date')
    return render(request, 'JW/business_mileage_list.html', {'mileages': mileages})

@login_required
def business_mileage_create(request):
    if request.method == 'POST':
        form = BusinessMileageForm(request.POST)
        if form.is_valid():
            mileage = form.save(commit=False)
            mileage.user = request.user
            mileage.mileage = form.cleaned_data['end_mileage'] - form.cleaned_data['start_mileage']
            mileage.save()
            messages.success(request, 'Business mileage created successfully.')
            return redirect('business_mileage_list')
    else:
        form = BusinessMileageForm()
    return render(request, 'JW/business_mileage_form.html', {'form': form, 'action': 'Create'})

@login_required
def business_mileage_update(request, pk):
    mileage = get_object_or_404(AppJwBusinessmileage, pk=pk)
    if request.method == 'POST':
        form = BusinessMileageForm(request.POST, instance=mileage)
        if form.is_valid():
            mileage = form.save(commit=False)
            mileage.mileage = form.cleaned_data['end_mileage'] - form.cleaned_data['start_mileage']
            mileage.save()
            messages.success(request, 'Business mileage updated successfully.')
            return redirect('business_mileage_list')
    else:
        form = BusinessMileageForm(instance=mileage)
    return render(request, 'JW/business_mileage_form.html', {'form': form, 'action': 'Update'})

@login_required
def business_mileage_delete(request, pk):
    mileage = get_object_or_404(AppJwBusinessmileage, pk=pk)
    if request.method == 'POST':
        mileage.delete()
        messages.success(request, 'Business mileage deleted successfully.')
        return redirect('business_mileage_list')

    record_details = {
        'Date': mileage.date,
        'Day': mileage.day_of_week,
        'Mileage': mileage.mileage,
        'Notes': mileage.notes
    }
    context = {
        'record_details': record_details,
        'cancel_url': reverse('business_mileage_list')
    }
    return render(request, 'JW/confirm_delete.html', context)

# Personal Mileage Views
@login_required
def personal_mileage_list(request):
    mileages = AppJwPersonalmileage.objects.all().order_by('-date')
    return render(request, 'JW/personal_mileage_list.html', {'mileages': mileages})

@login_required
def personal_mileage_create(request):
    if request.method == 'POST':
        form = PersonalMileageForm(request.POST)
        if form.is_valid():
            mileage = form.save(commit=False)
            mileage.user = request.user
            mileage.mileage = form.cleaned_data['end_mileage'] - form.cleaned_data['start_mileage']
            mileage.save()
            messages.success(request, 'Personal mileage created successfully.')
            return redirect('personal_mileage_list')
    else:
        form = PersonalMileageForm()
    return render(request, 'JW/personal_mileage_form.html', {'form': form, 'action': 'Create'})

@login_required
def personal_mileage_update(request, pk):
    mileage = get_object_or_404(AppJwPersonalmileage, pk=pk)
    if request.method == 'POST':
        form = PersonalMileageForm(request.POST, instance=mileage)
        if form.is_valid():
            mileage = form.save(commit=False)
            mileage.mileage = form.cleaned_data['end_mileage'] - form.cleaned_data['start_mileage']
            mileage.save()
            messages.success(request, 'Personal mileage updated successfully.')
            return redirect('personal_mileage_list')
    else:
        form = PersonalMileageForm(instance=mileage)
    return render(request, 'JW/personal_mileage_form.html', {'form': form, 'action': 'Update'})

@login_required
def personal_mileage_delete(request, pk):
    mileage = get_object_or_404(AppJwPersonalmileage, pk=pk)
    if request.method == 'POST':
        mileage.delete()
        messages.success(request, 'Personal mileage deleted successfully.')
        return redirect('personal_mileage_list')

    record_details = {
        'Date': mileage.date,
        'Day': mileage.day_of_week,
        'Mileage': mileage.mileage,
        'Notes': mileage.notes
    }
    context = {
        'record_details': record_details,
        'cancel_url': reverse('personal_mileage_list')
    }
    return render(request, 'JW/confirm_delete.html', context)

# Mileage Views
@login_required
def mileage_list(request):
    from datetime import datetime, timedelta
    from django.utils import timezone
    from decimal import Decimal

    mileage_entries = AppJwMileage.objects.all().order_by('-date')
    total_miles = sum(entry.miles or Decimal('0') for entry in mileage_entries)
    business_miles = sum(entry.miles or Decimal('0') for entry in mileage_entries if entry.mileage_type == 'Business')
    personal_miles = sum(entry.miles or Decimal('0') for entry in mileage_entries if entry.mileage_type == 'Personal')

    business_percentage = (business_miles / total_miles * 100) if total_miles > 0 else Decimal('0')
    personal_percentage = (personal_miles / total_miles * 100) if total_miles > 0 else Decimal('0')

    # Get lesson data for averages
    lessons = AppJwLesson.objects.all()
    total_lessons = Decimal(str(lessons.count()))
    total_lesson_hours = sum(Decimal(str(lesson.lesson_hours or 0)) for lesson in lessons)

    # Calculate new averages
    avg_miles_per_lesson = business_miles / total_lessons if total_lessons > 0 else Decimal('0')
    avg_miles_per_hour = business_miles / total_lesson_hours if total_lesson_hours > 0 else Decimal('0')

    # Calculate time-based averages (all-time data)
    now = timezone.now().date()

    # Get earliest mileage date to calculate time periods
    earliest_mileage = mileage_entries.last()
    if earliest_mileage:
        days_since_start = (now - earliest_mileage.date).days + 1
        weeks_since_start = Decimal(str(days_since_start)) / Decimal('7')
        months_since_start = Decimal(str(days_since_start)) / Decimal('30.44')  # Average days per month
        quarters_since_start = Decimal(str(days_since_start)) / Decimal('91.31')  # Average days per quarter
    else:
        weeks_since_start = months_since_start = quarters_since_start = Decimal('1')

    # All-time averages
    avg_miles_per_week_alltime = business_miles / weeks_since_start if weeks_since_start > 0 else Decimal('0')
    avg_miles_per_month_alltime = business_miles / months_since_start if months_since_start > 0 else Decimal('0')
    avg_miles_per_quarter_alltime = business_miles / quarters_since_start if quarters_since_start > 0 else Decimal('0')

    # Get fuel expenses for cost calculations
    fuel_expenses = AppJwFuelexpense.objects.all()
    total_fuel_cost = sum(Decimal(str(expense.cost or 0)) for expense in fuel_expenses)
    business_fuel_cost = (total_fuel_cost * business_percentage / 100) if total_miles > 0 else Decimal('0')

    # All-time fuel cost averages
    avg_fuel_cost_per_week_alltime = business_fuel_cost / weeks_since_start if weeks_since_start > 0 else Decimal('0')
    avg_fuel_cost_per_month_alltime = business_fuel_cost / months_since_start if months_since_start > 0 else Decimal('0')
    avg_fuel_cost_per_quarter_alltime = business_fuel_cost / quarters_since_start if quarters_since_start > 0 else Decimal('0')

    # Calculate current period statistics
    from datetime import datetime, timedelta
    import calendar

    # Current week (Monday to Sunday)
    today = now
    days_since_monday = today.weekday()
    week_start = today - timedelta(days=days_since_monday)
    week_end = week_start + timedelta(days=6)

    # Current month
    month_start = today.replace(day=1)
    month_end = today.replace(day=calendar.monthrange(today.year, today.month)[1])

    # Current quarter
    quarter = (today.month - 1) // 3 + 1
    quarter_start_month = (quarter - 1) * 3 + 1
    quarter_start = today.replace(month=quarter_start_month, day=1)
    if quarter == 4:
        quarter_end = today.replace(month=12, day=31)
    else:
        next_quarter_start_month = quarter * 3 + 1
        quarter_end = today.replace(month=next_quarter_start_month, day=1) - timedelta(days=1)

    # Current week statistics
    current_week_mileage = mileage_entries.filter(date__range=[week_start, week_end])
    current_week_business_miles = sum(entry.miles or Decimal('0') for entry in current_week_mileage if entry.mileage_type == 'Business')
    current_week_fuel_expenses = fuel_expenses.filter(date__range=[week_start, week_end])
    current_week_fuel_cost = sum(Decimal(str(expense.cost or 0)) for expense in current_week_fuel_expenses)
    current_week_business_fuel_cost = (current_week_fuel_cost * business_percentage / 100) if total_miles > 0 else Decimal('0')

    # Current month statistics
    current_month_mileage = mileage_entries.filter(date__range=[month_start, month_end])
    current_month_business_miles = sum(entry.miles or Decimal('0') for entry in current_month_mileage if entry.mileage_type == 'Business')
    current_month_fuel_expenses = fuel_expenses.filter(date__range=[month_start, month_end])
    current_month_fuel_cost = sum(Decimal(str(expense.cost or 0)) for expense in current_month_fuel_expenses)
    current_month_business_fuel_cost = (current_month_fuel_cost * business_percentage / 100) if total_miles > 0 else Decimal('0')

    # Current quarter statistics
    current_quarter_mileage = mileage_entries.filter(date__range=[quarter_start, quarter_end])
    current_quarter_business_miles = sum(entry.miles or Decimal('0') for entry in current_quarter_mileage if entry.mileage_type == 'Business')
    current_quarter_fuel_expenses = fuel_expenses.filter(date__range=[quarter_start, quarter_end])
    current_quarter_fuel_cost = sum(Decimal(str(expense.cost or 0)) for expense in current_quarter_fuel_expenses)
    current_quarter_business_fuel_cost = (current_quarter_fuel_cost * business_percentage / 100) if total_miles > 0 else Decimal('0')

    return render(request, 'JW/mileage_list.html', {
        'mileage_entries': mileage_entries,
        'total_miles': total_miles,
        'business_miles': business_miles,
        'personal_miles': personal_miles,
        'business_percentage': business_percentage,
        'personal_percentage': personal_percentage,
        'avg_miles_per_lesson': avg_miles_per_lesson,
        'avg_miles_per_hour': avg_miles_per_hour,
        # All-time averages
        'avg_miles_per_week_alltime': avg_miles_per_week_alltime,
        'avg_miles_per_month_alltime': avg_miles_per_month_alltime,
        'avg_miles_per_quarter_alltime': avg_miles_per_quarter_alltime,
        'avg_fuel_cost_per_week_alltime': avg_fuel_cost_per_week_alltime,
        'avg_fuel_cost_per_month_alltime': avg_fuel_cost_per_month_alltime,
        'avg_fuel_cost_per_quarter_alltime': avg_fuel_cost_per_quarter_alltime,
        # Current period statistics
        'current_week_business_miles': current_week_business_miles,
        'current_week_business_fuel_cost': current_week_business_fuel_cost,
        'current_month_business_miles': current_month_business_miles,
        'current_month_business_fuel_cost': current_month_business_fuel_cost,
        'current_quarter_business_miles': current_quarter_business_miles,
        'current_quarter_business_fuel_cost': current_quarter_business_fuel_cost,
    })

@login_required
def mileage_create(request):
    return_url = request.GET.get('return_url') or request.POST.get('return_url') or reverse('mileage_list')

    if request.method == 'POST':
        form = MileageForm(request.POST)
        if form.is_valid():
            mileage = form.save(commit=False)
            mileage.user = request.user
            mileage.save()
            messages.success(request, 'Mileage entry created successfully.')

            if request.POST.get('action') == 'save_and_add':
                return redirect(f"{reverse('mileage_create')}?return_url={return_url}")

            return redirect(return_url)
    else:
        form = MileageForm()

    context = {
        'form': form,
        'action': 'Add',
        'return_url': return_url
    }
    return render(request, 'JW/mileage_form.html', context)

@login_required
def mileage_update(request, pk):
    mileage = get_object_or_404(AppJwMileage, pk=pk)
    if request.method == 'POST':
        form = MileageForm(request.POST, instance=mileage)
        if form.is_valid():
            mileage = form.save(commit=False)
            if mileage.date:
                mileage.day = calendar.day_name[mileage.date.weekday()]
            mileage.save()
            messages.success(request, 'Mileage record updated successfully.')
            return redirect('mileage_list')
    else:
        # Ensure day is set based on the date
        initial = {
            'day': calendar.day_name[mileage.date.weekday()] if mileage.date else ''
        }
        form = MileageForm(instance=mileage, initial=initial)
    return render(request, 'JW/mileage_form.html', {'form': form, 'action': 'Update'})

@login_required
def mileage_delete(request, pk):
    mileage = get_object_or_404(AppJwMileage, pk=pk)
    if request.method == 'POST':
        mileage.delete()
        messages.success(request, 'Mileage record deleted successfully.')
        return redirect('mileage_list')
    return render(request, 'JW/mileage_confirm_delete.html', {'mileage': mileage})

# Fuel Expense Views
@login_required
def fuel_expense_list(request):
    from datetime import datetime, timedelta
    from django.utils import timezone
    import calendar

    # Get all fuel expenses
    expenses = AppJwFuelexpense.objects.all().order_by('-date')

    # Calculate totals and statistics
    total_cost = expenses.aggregate(total=Sum('cost'))['total'] or Decimal('0.00')
    total_items = expenses.count()

    # Calculate average cost
    avg_cost = total_cost / total_items if total_items > 0 else Decimal('0.00')

    # Calculate mileage percentages and costs
    total_miles = AppJwMileage.objects.aggregate(total=Sum('miles'))['total'] or Decimal('0.00')
    business_miles = AppJwMileage.objects.filter(mileage_type='Business').aggregate(total=Sum('miles'))['total'] or Decimal('0.00')
    personal_miles = AppJwMileage.objects.filter(mileage_type='Personal').aggregate(total=Sum('miles'))['total'] or Decimal('0.00')

    # Calculate percentages
    business_percentage = (business_miles / total_miles * 100) if total_miles > 0 else 0
    personal_percentage = (personal_miles / total_miles * 100) if total_miles > 0 else 0

    # Calculate business and personal fuel costs based on mileage percentages
    business_fuel_cost = (total_cost * business_percentage / 100) if total_miles > 0 else 0
    personal_fuel_cost = (total_cost * personal_percentage / 100) if total_miles > 0 else 0

    # Calculate time-based averages (moved from mileage page)
    now = timezone.now().date()

    # Get earliest fuel expense date to calculate time periods
    earliest_expense = expenses.last()
    if earliest_expense:
        days_since_start = (now - earliest_expense.date).days + 1
        weeks_since_start = Decimal(str(days_since_start)) / Decimal('7')
        months_since_start = Decimal(str(days_since_start)) / Decimal('30.44')  # Average days per month
        quarters_since_start = Decimal(str(days_since_start)) / Decimal('91.31')  # Average days per quarter
    else:
        weeks_since_start = months_since_start = quarters_since_start = Decimal('1')

    # All-time fuel cost averages
    avg_fuel_cost_per_week_alltime = business_fuel_cost / weeks_since_start if weeks_since_start > 0 else Decimal('0')
    avg_fuel_cost_per_month_alltime = business_fuel_cost / months_since_start if months_since_start > 0 else Decimal('0')
    avg_fuel_cost_per_quarter_alltime = business_fuel_cost / quarters_since_start if quarters_since_start > 0 else Decimal('0')

    # Calculate current period statistics
    # Current week (Monday to Sunday)
    today = now
    days_since_monday = today.weekday()
    week_start = today - timedelta(days=days_since_monday)
    week_end = week_start + timedelta(days=6)

    # Current month
    month_start = today.replace(day=1)
    month_end = today.replace(day=calendar.monthrange(today.year, today.month)[1])

    # Current quarter
    quarter = (today.month - 1) // 3 + 1
    quarter_start_month = (quarter - 1) * 3 + 1
    quarter_start = today.replace(month=quarter_start_month, day=1)
    if quarter == 4:
        quarter_end = today.replace(month=12, day=31)
    else:
        next_quarter_start_month = quarter * 3 + 1
        quarter_end = today.replace(month=next_quarter_start_month, day=1) - timedelta(days=1)

    # Current week statistics
    current_week_fuel_expenses = expenses.filter(date__range=[week_start, week_end])
    current_week_fuel_cost = sum(Decimal(str(expense.cost or 0)) for expense in current_week_fuel_expenses)
    current_week_business_fuel_cost = (current_week_fuel_cost * business_percentage / 100) if total_miles > 0 else Decimal('0')

    # Current month statistics
    current_month_fuel_expenses = expenses.filter(date__range=[month_start, month_end])
    current_month_fuel_cost = sum(Decimal(str(expense.cost or 0)) for expense in current_month_fuel_expenses)
    current_month_business_fuel_cost = (current_month_fuel_cost * business_percentage / 100) if total_miles > 0 else Decimal('0')

    # Current quarter statistics
    current_quarter_fuel_expenses = expenses.filter(date__range=[quarter_start, quarter_end])
    current_quarter_fuel_cost = sum(Decimal(str(expense.cost or 0)) for expense in current_quarter_fuel_expenses)
    current_quarter_business_fuel_cost = (current_quarter_fuel_cost * business_percentage / 100) if total_miles > 0 else Decimal('0')

    context = {
        'expenses': expenses,
        'total_cost': total_cost,
        'avg_cost': avg_cost,  # Changed from total_items
        'business_fuel_cost': business_fuel_cost,
        'personal_fuel_cost': personal_fuel_cost,
        'business_percentage': business_percentage,
        'personal_percentage': personal_percentage,
        # All-time averages (moved from mileage page)
        'avg_fuel_cost_per_week_alltime': avg_fuel_cost_per_week_alltime,
        'avg_fuel_cost_per_month_alltime': avg_fuel_cost_per_month_alltime,
        'avg_fuel_cost_per_quarter_alltime': avg_fuel_cost_per_quarter_alltime,
        # Current period statistics (moved from mileage page)
        'current_week_business_fuel_cost': current_week_business_fuel_cost,
        'current_month_business_fuel_cost': current_month_business_fuel_cost,
        'current_quarter_business_fuel_cost': current_quarter_business_fuel_cost,
    }
    return render(request, 'JW/fuel_expense_list.html', context)

@login_required
def fuel_expense_create(request):
    return_url = request.GET.get('return_url') or request.POST.get('return_url') or reverse('fuel_expense_list')

    if request.method == 'POST':
        form = FuelExpenseForm(request.POST)
        if form.is_valid():
            expense = form.save(commit=False)
            expense.user = request.user
            expense.save()
            messages.success(request, 'Fuel expense created successfully.')

            if request.POST.get('action') == 'save_and_add':
                return redirect(f"{reverse('fuel_expense_create')}?return_url={return_url}")

            return redirect(return_url)
    else:
        form = FuelExpenseForm()

    context = {
        'form': form,
        'action': 'Add',
        'return_url': return_url
    }
    return render(request, 'JW/fuel_expense_form.html', context)

@login_required
def fuel_expense_update(request, pk):
    expense = get_object_or_404(AppJwFuelexpense, pk=pk)
    if request.method == 'POST':
        form = FuelExpenseForm(request.POST, instance=expense)
        if form.is_valid():
            form.save()
            messages.success(request, 'Fuel expense updated successfully.')
            return redirect('fuel_expense_list')
    else:
        form = FuelExpenseForm(instance=expense)

    context = {
        'form': form,
        'action': 'Update',
    }
    return render(request, 'JW/fuel_expense_form.html', context)

@login_required
def fuel_expense_delete(request, pk):
    expense = get_object_or_404(AppJwFuelexpense, pk=pk)
    if request.method == 'POST':
        expense.delete()
        messages.success(request, 'Fuel expense deleted successfully.')
        return redirect('fuel_expense_list')

    context = {
        'object': expense,
    }
    return render(request, 'JW/fuel_expense_confirm_delete.html', context)

# Report Views
@login_required
def report_list(request):
    # Get all dates that have either lessons, business expenses, or fuel expenses
    lesson_dates = set(AppJwLesson.objects.dates('date', 'month'))
    business_expense_dates = set(AppJwBusinessexpense.objects.dates('date', 'month'))
    fuel_expense_dates = set(AppJwFuelexpense.objects.dates('date', 'month'))

    # Combine all dates and sort them
    all_dates = sorted(lesson_dates | business_expense_dates | fuel_expense_dates, reverse=True)

    # Get available tax years
    tax_years = {}
    if all_dates:
        earliest_date = min(all_dates)
        latest_date = max(all_dates)

        # Find the first and last tax years
        # Fix: Adjust the logic for determining tax years
        first_tax_year = earliest_date.year if earliest_date.month >= 4 else earliest_date.year - 1
        last_tax_year = latest_date.year if latest_date.month >= 4 else latest_date.year - 1

        # Create tax year entries
        for year in range(last_tax_year, first_tax_year - 1, -1):
            tax_year_start = datetime(year, 4, 6).date()  # Tax year starts April 6th
            tax_year_end = datetime(year + 1, 4, 5).date()  # Tax year ends April 5th

            # Check if there's any data in this tax year
            has_data = (
                AppJwLesson.objects.filter(date__range=(tax_year_start, tax_year_end)).exists() or
                AppJwBusinessexpense.objects.filter(date__range=(tax_year_start, tax_year_end)).exists() or
                AppJwFuelexpense.objects.filter(date__range=(tax_year_start, tax_year_end)).exists() or
                AppJwMileage.objects.filter(date__range=(tax_year_start, tax_year_end)).exists()  # Added mileage check
            )

            if has_data:
                tax_years[year] = {
                    'start_date': tax_year_start,
                    'end_date': tax_year_end
                }

    # Define a list of primary colors for year headers
    primary_colors = [
        'primary',   # Blue
        'success',   # Green
        'danger',    # Red
        'info',      # Light Blue
        'secondary', # Gray
        'dark',      # Dark
        'purple',    # Purple (custom)
        'teal',      # Teal (custom)
        'indigo',    # Indigo (custom)
        'pink'       # Pink (custom)
    ]

    # Group dates by year and assign colors
    reports_by_year = {}
    for date in all_dates:
        year = date.year
        if year not in reports_by_year:
            # Assign color based on year modulo number of colors
            color_index = (year % len(primary_colors))
            reports_by_year[year] = {
                'months': [],
                'color': primary_colors[color_index]
            }
        reports_by_year[year]['months'].append({
            'month_num': date.month,
            'month_name': calendar.month_name[date.month],
            'year': year
        })

    context = {
        'reports_by_year': reports_by_year,
        'tax_years': tax_years
    }
    return render(request, 'JW/report_list.html', context)

@login_required
def monthly_report(request):
    month = request.GET.get('month', datetime.now().month)
    year = request.GET.get('year', datetime.now().year)
    month = int(month)
    year = int(year)

    # Get the month name
    month_name = calendar.month_name[month]

    # Get all data for the month
    start_date = datetime(year, month, 1)
    end_date = datetime(year, month, calendar.monthrange(year, month)[1])

    # Get all data for the month
    lessons = AppJwLesson.objects.filter(
        date__month=month,
        date__year=year
    ).order_by('date')

    business_expenses = AppJwBusinessexpense.objects.filter(
        date__month=month,
        date__year=year
    ).order_by('date')

    fuel_expenses = AppJwFuelexpense.objects.filter(
        date__month=month,
        date__year=year
    ).order_by('date')

    # Calculate totals
    total_income = sum(
        lesson.amount or (lesson.lesson_hours * lesson.price_per_hour)
        for lesson in lessons
    )
    total_business_expenses = sum(expense.cost for expense in business_expenses)
    total_fuel_expenses = sum(expense.cost for expense in fuel_expenses)
    total_expenses = total_business_expenses + total_fuel_expenses
    net_income = total_income - total_expenses

    # Get mileage entries
    mileage_entries = AppJwMileage.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    )

    total_miles = mileage_entries.aggregate(
        total=Sum('miles'))['total'] or Decimal('0.0')

    business_miles = mileage_entries.filter(
        mileage_type='Business'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    personal_miles = mileage_entries.filter(
        mileage_type='Personal'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    # Calculate percentages
    business_miles_percentage = Decimal('0.0')
    personal_miles_percentage = Decimal('0.0')
    if total_miles > 0:
        business_miles_percentage = (business_miles / total_miles) * 100
        personal_miles_percentage = (personal_miles / total_miles) * 100

    context = {
        'month': month,
        'year': year,
        'month_name': month_name,
        'lessons': lessons,
        'business_expenses': business_expenses,
        'fuel_expenses': fuel_expenses,
        'total_income': total_income,
        'total_business_expenses': total_business_expenses,
        'total_fuel_expenses': total_fuel_expenses,
        'net_income': net_income,
        'total_miles': total_miles,
        'total_business_miles': business_miles,
        'total_personal_miles': personal_miles,
        'business_miles_percentage': business_miles_percentage,
        'personal_miles_percentage': personal_miles_percentage,
        'report_header': get_report_header(request.user)
    }

    return render(request, 'JW/monthly_report.html', context)

@login_required
@cache_page(60 * 15)  # Cache for 15 minutes
def monthly_report_summary(request):
    month = request.GET.get('month', datetime.now().month)
    year = request.GET.get('year', datetime.now().year)
    month = int(month)
    year = int(year)

    # Get the month name
    month_name = calendar.month_name[month]

    # Get all data for the month
    lessons = AppJwLesson.objects.filter(
        date__month=month,
        date__year=year
    ).select_related('student')  # If there's a student relation

    # Calculate lesson statistics
    total_lessons = lessons.count()
    total_hours = sum(lesson.lesson_hours for lesson in lessons)
    total_income = sum(
        lesson.amount or (lesson.lesson_hours * lesson.price_per_hour)
        for lesson in lessons
    )
    avg_price_per_hour = total_income / total_hours if total_hours > 0 else 0

    # Get expense totals
    total_business_expenses = AppJwBusinessexpense.objects.filter(
        date__month=month,
        date__year=year
    ).aggregate(total=Sum('cost'))['total'] or 0

    total_fuel_expenses = AppJwFuelexpense.objects.filter(
        date__month=month,
        date__year=year
    ).aggregate(total=Sum('cost'))['total'] or 0

    # Calculate mileage statistics
    mileage = AppJwMileage.objects.filter(
        date__month=month,
        date__year=year
    )
    total_miles = mileage.aggregate(total=Sum('miles'))['total'] or 0
    total_business_miles = mileage.filter(mileage_type='Business').aggregate(total=Sum('miles'))['total'] or 0
    total_personal_miles = mileage.filter(mileage_type='Personal').aggregate(total=Sum('miles'))['total'] or 0

    # Calculate percentages
    business_miles_percentage = (total_business_miles / total_miles * 100) if total_miles > 0 else 0
    personal_miles_percentage = (total_personal_miles / total_miles * 100) if total_miles > 0 else 0

    # Calculate net income
    net_income = total_income - total_business_expenses - total_fuel_expenses

    context = {
        'month': month,
        'year': year,
        'month_name': month_name,
        'total_lessons': total_lessons,
        'total_hours': total_hours,
        'avg_price_per_hour': avg_price_per_hour,
        'total_income': total_income,
        'total_business_expenses': total_business_expenses,
        'total_fuel_expenses': total_fuel_expenses,
        'total_miles': total_miles,
        'total_business_miles': total_business_miles,
        'total_personal_miles': total_personal_miles,
        'business_miles_percentage': business_miles_percentage,
        'personal_miles_percentage': personal_miles_percentage,
        'net_income': net_income,
        'report_header': get_report_header(request.user)
    }

    return render(request, 'JW/monthly_report_summary.html', context)

@login_required
def monthly_report_summary_pdf(request):
    month = request.GET.get('month', datetime.now().month)
    year = request.GET.get('year', datetime.now().year)
    month = int(month)
    year = int(year)

    # Get the month name
    month_name = calendar.month_name[month]

    # Get all data for the month
    lessons = AppJwLesson.objects.filter(
        date__month=month,
        date__year=year
    )

    # Calculate lesson statistics
    total_lessons = lessons.count()
    total_hours = sum(lesson.lesson_hours for lesson in lessons)
    total_income = sum(
        lesson.amount or (lesson.lesson_hours * lesson.price_per_hour)
        for lesson in lessons
    )

    # Calculate average price per hour
    avg_price_per_hour = Decimal('0.0')
    if total_hours > 0:
        avg_price_per_hour = total_income / total_hours

    # Get business expenses
    business_expenses = AppJwBusinessexpense.objects.filter(
        date__month=month,
        date__year=year
    )
    total_business_expenses = sum(expense.cost for expense in business_expenses)

    # Get fuel expenses
    fuel_expenses = AppJwFuelexpense.objects.filter(
        date__month=month,
        date__year=year
    )
    total_fuel_expenses = sum(expense.cost for expense in fuel_expenses)

    # Calculate mileage using AppJwMileage model
    mileage_entries = AppJwMileage.objects.filter(
        date__month=month,
        date__year=year
    )

    total_miles = mileage_entries.aggregate(
        total=Sum('miles'))['total'] or Decimal('0.0')

    business_miles = mileage_entries.filter(
        mileage_type='Business'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    personal_miles = mileage_entries.filter(
        mileage_type='Personal'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    # Calculate percentages
    business_miles_percentage = Decimal('0.0')
    personal_miles_percentage = Decimal('0.0')
    if total_miles > 0:
        business_miles_percentage = (business_miles / total_miles) * 100
        personal_miles_percentage = (personal_miles / total_miles) * 100

    # Calculate net income
    net_income = total_income - total_business_expenses - total_fuel_expenses

    context = {
        'month': month,
        'year': year,
        'month_name': month_name,
        'total_lessons': total_lessons,
        'total_hours': total_hours,
        'avg_price_per_hour': avg_price_per_hour,
        'total_income': total_income,
        'total_business_expenses': total_business_expenses,
        'total_fuel_expenses': total_fuel_expenses,
        'total_miles': total_miles,
        'total_business_miles': business_miles,
        'total_personal_miles': personal_miles,
        'business_miles_percentage': business_miles_percentage,
        'personal_miles_percentage': personal_miles_percentage,
        'net_income': net_income,
        'report_header': get_report_header(request.user, format_html=True)
    }

    # Generate PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="monthly_report_summary_{month}_{year}.pdf"'

    html_string = render_to_string('JW/monthly_report_summary_pdf.html', context)
    pisa_status = pisa.CreatePDF(html_string, dest=response)

    if pisa_status.err:
        return HttpResponse('We had some errors creating the PDF')

    return response

@login_required
def monthly_report_pdf(request):
    month = request.GET.get('month', datetime.now().month)
    year = request.GET.get('year', datetime.now().year)
    month = int(month)
    year = int(year)

    # Get the month name
    month_name = calendar.month_name[month]

    # Get all data for the month
    start_date = datetime(year, month, 1)
    end_date = datetime(year, month, calendar.monthrange(year, month)[1])

    # Get all data for the month
    lessons = AppJwLesson.objects.filter(
        date__month=month,
        date__year=year
    ).order_by('date')

    business_expenses = AppJwBusinessexpense.objects.filter(
        date__month=month,
        date__year=year
    ).order_by('date')

    fuel_expenses = AppJwFuelexpense.objects.filter(
        date__month=month,
        date__year=year
    ).order_by('date')

    # Calculate totals
    total_income = sum(
        lesson.amount or (lesson.lesson_hours * lesson.price_per_hour)
        for lesson in lessons
    )
    total_business_expenses = sum(expense.cost for expense in business_expenses)
    total_fuel_expenses = sum(expense.cost for expense in fuel_expenses)
    total_expenses = total_business_expenses + total_fuel_expenses
    net_income = total_income - total_expenses

    # Mileage calculations for PDF report
    mileage_entries = AppJwMileage.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    )

    total_miles = mileage_entries.aggregate(
        total=Sum('miles'))['total'] or Decimal('0.0')

    business_miles = mileage_entries.filter(
        mileage_type='Business'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    personal_miles = mileage_entries.filter(
        mileage_type='Personal'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    # Calculate percentages
    business_miles_percentage = Decimal('0.0')
    personal_miles_percentage = Decimal('0.0')
    if total_miles > 0:
        business_miles_percentage = (business_miles / total_miles) * 100
        personal_miles_percentage = (personal_miles / total_miles) * 100

    context = {
        'month': month,
        'year': year,
        'month_name': month_name,
        'lessons': lessons,
        'business_expenses': business_expenses,
        'fuel_expenses': fuel_expenses,
        'total_income': total_income,
        'total_business_expenses': total_business_expenses,
        'total_fuel_expenses': total_fuel_expenses,
        'net_income': net_income,
        'total_miles': total_miles,
        'total_business_miles': business_miles,
        'total_personal_miles': personal_miles,
        'business_miles_percentage': business_miles_percentage,
        'personal_miles_percentage': personal_miles_percentage,
        'report_header': get_report_header(request.user)
    }

    # Render the PDF template
    template = get_template('JW/monthly_report_pdf.html')
    html = template.render(context)

    # Create the PDF
    result = BytesIO()
    pdf = pisa.pisaDocument(BytesIO(html.encode("UTF-8")), result)

    if not pdf.err:
        return HttpResponse(result.getvalue(), content_type='application/pdf')
    return HttpResponse('Error generating PDF', status=500)

@login_required
def tax_year_report(request):
    tax_year_start = request.GET.get('start_year')
    if tax_year_start:
        tax_year_start = int(tax_year_start)
    else:
        # Default to current tax year
        today = datetime.now().date()
        if today.month < 4 or (today.month == 4 and today.day < 6):
            tax_year_start = today.year - 1
        else:
            tax_year_start = today.year

    # Define tax year date range
    start_date = datetime(tax_year_start, 4, 6).date()
    end_date = datetime(tax_year_start + 1, 4, 5).date()
    tax_year_display = f"{tax_year_start}/{str(tax_year_start + 1)[2:]}"

    # Get all data within the tax year date range
    lessons = AppJwLesson.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')

    business_expenses = AppJwBusinessexpense.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')

    fuel_expenses = AppJwFuelexpense.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')

    mileage_entries = AppJwMileage.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')

    # Calculate totals
    total_income = sum(
        lesson.amount or (lesson.lesson_hours * lesson.price_per_hour)
        for lesson in lessons
    )
    total_business_expenses = sum(expense.cost for expense in business_expenses)
    total_fuel_expenses = sum(expense.cost for expense in fuel_expenses)
    total_expenses = total_business_expenses + total_fuel_expenses
    net_income = total_income - total_expenses

    # Mileage calculations for full report
    mileage_entries = AppJwMileage.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    )

    total_miles = mileage_entries.aggregate(
        total=Sum('miles'))['total'] or Decimal('0.0')

    business_miles = mileage_entries.filter(
        mileage_type='Business'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    personal_miles = mileage_entries.filter(
        mileage_type='Personal'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    # Calculate percentages
    business_miles_percentage = Decimal('0.0')
    personal_miles_percentage = Decimal('0.0')
    if total_miles > 0:
        business_miles_percentage = (business_miles / total_miles) * 100
        personal_miles_percentage = (personal_miles / total_miles) * 100

    context = {
        'tax_year_display': tax_year_display,
        'start_date': start_date,
        'end_date': end_date,
        'lessons': lessons,
        'business_expenses': business_expenses,
        'fuel_expenses': fuel_expenses,
        'total_income': total_income,
        'total_business_expenses': total_business_expenses,
        'total_fuel_expenses': total_fuel_expenses,
        'total_expenses': total_expenses,
        'net_income': net_income,
        'mileage_entries': mileage_entries,
        'total_miles': total_miles,
        'total_business_miles': business_miles,
        'total_personal_miles': personal_miles,
        'business_miles_percentage': business_miles_percentage,
        'personal_miles_percentage': personal_miles_percentage,
        'report_header': get_report_header(request.user)
    }

    return render(request, 'JW/tax_year_report.html', context)

@login_required
def tax_year_report_pdf(request):
    # Reuse the same logic as tax_year_report
    tax_year_start = request.GET.get('start_year')
    if tax_year_start:
        tax_year_start = int(tax_year_start)
    else:
        today = datetime.now().date()
        if today.month < 4 or (today.month == 4 and today.day < 6):
            tax_year_start = today.year - 1
        else:
            tax_year_start = today.year

    start_date = datetime(tax_year_start, 4, 6).date()
    end_date = datetime(tax_year_start + 1, 4, 5).date()
    tax_year_display = f"{tax_year_start}/{str(tax_year_start + 1)[2:]}"

    # Get all data and calculate totals (same as tax_year_report)
    lessons = AppJwLesson.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')

    business_expenses = AppJwBusinessexpense.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')

    fuel_expenses = AppJwFuelexpense.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')

    mileage_entries = AppJwMileage.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')

    total_income = sum(
        lesson.amount or (lesson.lesson_hours * lesson.price_per_hour)
        for lesson in lessons
    )
    total_business_expenses = sum(expense.cost for expense in business_expenses)
    total_fuel_expenses = sum(expense.cost for expense in fuel_expenses)
    total_expenses = total_business_expenses + total_fuel_expenses
    net_income = total_income - total_expenses

    # Mileage calculations for PDF report
    mileage_entries = AppJwMileage.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    )

    total_miles = mileage_entries.aggregate(
        total=Sum('miles'))['total'] or Decimal('0.0')

    business_miles = mileage_entries.filter(
        mileage_type='Business'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    personal_miles = mileage_entries.filter(
        mileage_type='Personal'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    # Calculate percentages
    business_miles_percentage = Decimal('0.0')
    personal_miles_percentage = Decimal('0.0')
    if total_miles > 0:
        business_miles_percentage = (business_miles / total_miles) * 100
        personal_miles_percentage = (personal_miles / total_miles) * 100

    context = {
        'tax_year_display': tax_year_display,
        'start_date': start_date,
        'end_date': end_date,
        'lessons': lessons,
        'business_expenses': business_expenses,
        'fuel_expenses': fuel_expenses,
        'total_income': total_income,
        'total_business_expenses': total_business_expenses,
        'total_fuel_expenses': total_fuel_expenses,
        'total_expenses': total_expenses,
        'net_income': net_income,
        'mileage_entries': mileage_entries,
        'total_miles': total_miles,
        'total_business_miles': business_miles,
        'total_personal_miles': personal_miles,
        'business_miles_percentage': business_miles_percentage,
        'personal_miles_percentage': personal_miles_percentage,
        'report_header': get_report_header(request.user)
    }

    # Generate PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="tax_year_report_{tax_year_display}.pdf"'

    html_string = render_to_string('JW/tax_year_report_pdf.html', context)
    pisa_status = pisa.CreatePDF(html_string, dest=response)

    if pisa_status.err:
        return HttpResponse('We had some errors creating the PDF')

    return response

@login_required
def tax_year_report_summary(request):
    tax_year_start = request.GET.get('start_year')
    if tax_year_start:
        tax_year_start = int(tax_year_start)
    else:
        # Default to current tax year
        today = datetime.now().date()
        if today.month < 4 or (today.month == 4 and today.day < 6):
            tax_year_start = today.year - 1
        else:
            tax_year_start = today.year

    # Define tax year date range
    start_date = datetime(tax_year_start, 4, 6).date()
    end_date = datetime(tax_year_start + 1, 4, 5).date()
    tax_year_display = f"{tax_year_start}/{str(tax_year_start + 1)[2:]}"

    # Get all data within the tax year date range
    lessons = AppJwLesson.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    )

    # Calculate lesson statistics
    total_lessons = lessons.count()
    total_hours = lessons.aggregate(Sum('lesson_hours'))['lesson_hours__sum'] or Decimal('0.00')
    total_income = sum(
        lesson.amount or (lesson.lesson_hours * lesson.price_per_hour)
        for lesson in lessons
    )

    # Calculate average price per hour
    avg_price_per_hour = Decimal('0.00')
    if total_hours > 0:
        avg_price_per_hour = total_income / total_hours

    # Get expenses
    business_expenses = AppJwBusinessexpense.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).aggregate(Sum('cost'))['cost__sum'] or Decimal('0.00')

    fuel_expenses = AppJwFuelexpense.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).aggregate(Sum('cost'))['cost__sum'] or Decimal('0.00')

    net_income = total_income - (business_expenses + fuel_expenses)

    # Add mileage calculations
    mileage_entries = AppJwMileage.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    )

    total_miles = mileage_entries.aggregate(
        total=Sum('miles'))['total'] or Decimal('0.0')

    business_miles = mileage_entries.filter(
        mileage_type='Business'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    personal_miles = mileage_entries.filter(
        mileage_type='Personal'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    # Calculate percentages
    business_miles_percentage = (business_miles / total_miles * 100) if total_miles > 0 else Decimal('0.0')
    personal_miles_percentage = (personal_miles / total_miles * 100) if total_miles > 0 else Decimal('0.0')

    context = {
        'tax_year_display': tax_year_display,
        'start_date': start_date,
        'total_lessons': total_lessons,
        'total_hours': total_hours,
        'avg_price_per_hour': avg_price_per_hour,
        'total_income': total_income,
        'total_business_expenses': business_expenses,
        'total_fuel_expenses': fuel_expenses,
        'net_income': net_income,
        'total_miles': total_miles,
        'total_business_miles': business_miles,
        'total_personal_miles': personal_miles,
        'business_miles_percentage': business_miles_percentage,
        'personal_miles_percentage': personal_miles_percentage,
        'report_header': get_report_header(request.user)
    }

    return render(request, 'JW/tax_year_report_summary.html', context)

@login_required
def tax_year_report_summary_pdf(request):
    # Reuse the same logic as tax_year_report_summary
    tax_year_start = request.GET.get('start_year')
    if tax_year_start:
        tax_year_start = int(tax_year_start)
    else:
        today = datetime.now().date()
        if today.month < 4 or (today.month == 4 and today.day < 6):
            tax_year_start = today.year - 1
        else:
            tax_year_start = today.year

    start_date = datetime(tax_year_start, 4, 6).date()
    end_date = datetime(tax_year_start + 1, 4, 5).date()
    tax_year_display = f"{tax_year_start}/{str(tax_year_start + 1)[2:]}"

    lessons = AppJwLesson.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    )

    total_lessons = lessons.count()
    total_hours = lessons.aggregate(Sum('lesson_hours'))['lesson_hours__sum'] or Decimal('0.00')
    total_income = sum(
        lesson.amount or (lesson.lesson_hours * lesson.price_per_hour)
        for lesson in lessons
    )

    # Calculate average price per hour
    avg_price_per_hour = Decimal('0.00')
    if total_hours > 0:
        avg_price_per_hour = total_income / total_hours

    # Get expenses
    business_expenses = AppJwBusinessexpense.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).aggregate(Sum('cost'))['cost__sum'] or Decimal('0.00')

    fuel_expenses = AppJwFuelexpense.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).aggregate(Sum('cost'))['cost__sum'] or Decimal('0.00')

    net_income = total_income - (business_expenses + fuel_expenses)

    # Add mileage calculations
    mileage_entries = AppJwMileage.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    )

    total_miles = mileage_entries.aggregate(
        total=Sum('miles'))['total'] or Decimal('0.0')

    business_miles = mileage_entries.filter(
        mileage_type='Business'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    personal_miles = mileage_entries.filter(
        mileage_type='Personal'
    ).aggregate(total=Sum('miles'))['total'] or Decimal('0.0')

    # Calculate percentages
    business_miles_percentage = (business_miles / total_miles * 100) if total_miles > 0 else Decimal('0.0')
    personal_miles_percentage = (personal_miles / total_miles * 100) if total_miles > 0 else Decimal('0.0')

    context = {
        'tax_year_display': tax_year_display,
        'start_date': start_date,
        'total_lessons': total_lessons,
        'total_hours': total_hours,
        'avg_price_per_hour': avg_price_per_hour,
        'total_income': total_income,
        'total_business_expenses': business_expenses,
        'total_fuel_expenses': fuel_expenses,
        'net_income': net_income,
        'total_miles': total_miles,
        'total_business_miles': business_miles,
        'total_personal_miles': personal_miles,
        'business_miles_percentage': business_miles_percentage,
        'personal_miles_percentage': personal_miles_percentage,
        'report_header': get_report_header(request.user)
    }

    # Generate PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="tax_year_report_summary_{tax_year_display}.pdf"'

    html_string = render_to_string('JW/tax_year_report_summary_pdf.html', context)
    pisa_status = pisa.CreatePDF(html_string, dest=response)

    if pisa_status.err:
        return HttpResponse('We had some errors creating the PDF')

@login_required
def stats_view(request):
    from .models import AppJwUserSettings

    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # Get user's tax/NI percentage
    try:
        user_settings = AppJwUserSettings.objects.get(user=request.user)
        tax_ni_percentage = user_settings.tax_ni_percentage
    except AppJwUserSettings.DoesNotExist:
        tax_ni_percentage = Decimal('21.00')  # Default value

    def format_stats(raw_stats):
        """Helper function to format statistics"""
        net_earnings = raw_stats['net_earnings']
        after_tax_earnings = net_earnings * (Decimal('100') - tax_ni_percentage) / Decimal('100')

        return {
            'total_lessons': raw_stats['lesson_count'],
            'total_hours': raw_stats['total_hours'],
            'total_income': raw_stats['total_income'],
            'total_expenses': raw_stats['total_expenses'],
            'net_earnings': net_earnings,
            'after_tax_earnings': after_tax_earnings,
            'total_miles': raw_stats['total_miles'],
            'business_miles': raw_stats['business_miles'],
            'personal_miles': raw_stats['personal_miles']
        }

    # Get dates for different periods
    today = timezone.now().date()
    yesterday = today - timedelta(days=1)
    week_ago = yesterday - timedelta(days=6)  # Last 7 full days
    month_ago = today - timedelta(days=30)

    # Calculate current week dates (Monday-Sunday)
    days_until_sunday = (6 - today.weekday()) % 7
    current_week_end = today + timedelta(days=days_until_sunday)
    current_week_start = current_week_end - timedelta(days=6)  # This will be Monday

    # Get real-time statistics
    overall_stats = format_stats(get_stats())
    last_seven_days_stats = format_stats(get_stats(week_ago, yesterday))  # Last 7 full days
    current_week_stats = format_stats(get_stats(current_week_start, current_week_end))  # Current week
    monthly_stats = format_stats(get_stats(month_ago, today))

    # Weekly Trends
    weekly_trends = []
    today = timezone.now().date()

    # Calculate the most recent Sunday (end of current week)
    days_until_sunday = (6 - today.weekday()) % 7  # 6 represents Sunday
    current_week_end = today + timedelta(days=days_until_sunday)

    for i in range(8):
        week_end = current_week_end - timedelta(days=(i * 7))
        week_start = week_end - timedelta(days=6)  # This will be Monday

        # Get lesson statistics
        lesson_stats = AppJwLesson.objects.filter(
            date__gte=week_start,
            date__lte=week_end
        ).aggregate(
            total_income=Sum(
                Case(
                    When(amount__isnull=False, then='amount'),
                    default=F('lesson_hours') * F('price_per_hour'),
                    output_field=DecimalField()
                )
            ),
            total_hours=Sum('lesson_hours'),
            lesson_count=Count('id'),
            unique_students=Count('student', distinct=True)
        )

        # Get business expenses
        business_expenses = AppJwBusinessexpense.objects.filter(
            date__gte=week_start,
            date__lte=week_end
        ).aggregate(
            total_expenses=Sum('cost')
        )['total_expenses'] or Decimal('0.00')

        # Get fuel expenses
        fuel_expenses = AppJwFuelexpense.objects.filter(
            date__gte=week_start,
            date__lte=week_end
        ).aggregate(
            total_fuel=Sum('cost')
        )['total_fuel'] or Decimal('0.00')

        # Calculate totals ensuring Decimal precision
        total_income = lesson_stats['total_income'] or Decimal('0.00')
        total_expenses = business_expenses + fuel_expenses
        net_earnings = total_income - total_expenses

        # Calculate earnings per hour (after expenses)
        total_hours = lesson_stats['total_hours'] or Decimal('0.00')
        earnings_per_hour = Decimal('0.00')
        after_tax_earnings = net_earnings * (Decimal('100') - tax_ni_percentage) / Decimal('100')
        after_tax_earnings_per_hour = Decimal('0.00')

        if total_hours > 0:
            earnings_per_hour = net_earnings / total_hours
            after_tax_earnings_per_hour = after_tax_earnings / total_hours

        weekly_trends.append({
            'week': week_start,  # This will be Monday
            'week_end': week_end,  # This will be Sunday
            'lesson_count': lesson_stats['lesson_count'] or 0,
            'unique_students': lesson_stats['unique_students'] or 0,
            'total_hours': lesson_stats['total_hours'] or Decimal('0.00'),
            'total_income': total_income,
            'total_expenses': total_expenses,
            'net_earnings': net_earnings,
            'after_tax_earnings': after_tax_earnings,
            'earnings_per_hour': earnings_per_hour,
            'after_tax_earnings_per_hour': after_tax_earnings_per_hour
        })

    # Monthly and Quarterly Trends
    current_date = today
    current_year = current_date.year
    current_quarter = ((current_date.month - 1) // 3) + 1  # 1-4

    quarterly_trends = []
    monthly_trends = []

    # Calculate the last 4 quarters (current quarter + previous 3)
    for i in range(4):  # Show 4 quarters of data
        quarter = current_quarter - i
        year = current_year

        # Adjust year if we go back to previous year
        while quarter <= 0:
            quarter += 4
            year -= 1

        # Calculate quarter dates
        quarter_start = datetime(year, ((quarter - 1) * 3) + 1, 1).date()
        if quarter == 4:
            quarter_end = datetime(year, 12, 31).date()
        else:
            quarter_end = datetime(year, quarter * 3, 1).date() + relativedelta(months=1) - timedelta(days=1)

        # Get stats for the quarter
        quarter_stats = get_stats(quarter_start, quarter_end)

        # Ensure proper decimal handling
        total_hours = quarter_stats['total_hours'] or Decimal('0.00')
        net_earnings = quarter_stats['net_earnings'] or Decimal('0.00')
        earnings_per_hour = Decimal('0.00')
        after_tax_earnings = net_earnings * (Decimal('100') - tax_ni_percentage) / Decimal('100')
        after_tax_earnings_per_hour = Decimal('0.00')

        if total_hours > 0:
            earnings_per_hour = net_earnings / total_hours
            after_tax_earnings_per_hour = after_tax_earnings / total_hours

        quarterly_trends.append({
            'quarter_number': quarter,
            'year': year,
            'quarter_start': quarter_start,
            'quarter_end': quarter_end,
            'lesson_count': quarter_stats['lesson_count'],
            'unique_students': quarter_stats['unique_students'],
            'total_hours': total_hours,
            'total_income': quarter_stats['total_income'],
            'total_expenses': quarter_stats['total_expenses'],
            'net_earnings': net_earnings,
            'after_tax_earnings': after_tax_earnings,
            'earnings_per_hour': earnings_per_hour,
            'after_tax_earnings_per_hour': after_tax_earnings_per_hour
        })

        # Get months for this quarter
        month_start = quarter_start
        while month_start <= quarter_end:
            month_end = min(
                quarter_end,
                (month_start + relativedelta(months=1)) - timedelta(days=1)
            )

            # Skip future months (but include current month and past months)
            # Check if this is a future month (not the current month)
            if month_start.month > today.month and month_start.year >= today.year:
                month_start = month_start + relativedelta(months=1)
                continue

            month_stats = get_stats(month_start, month_end)

            # Calculate earnings per hour
            total_hours = month_stats['total_hours'] or Decimal('0.00')
            net_earnings = month_stats['net_earnings'] or Decimal('0.00')
            earnings_per_hour = Decimal('0.00')
            after_tax_earnings = net_earnings * (Decimal('100') - tax_ni_percentage) / Decimal('100')
            after_tax_earnings_per_hour = Decimal('0.00')

            if total_hours > 0:
                earnings_per_hour = net_earnings / total_hours
                after_tax_earnings_per_hour = after_tax_earnings / total_hours

            monthly_trends.append({
                'month': month_start,
                'month_end': month_end,
                'lesson_count': month_stats['lesson_count'],
                'unique_students': month_stats['unique_students'],
                'total_hours': month_stats['total_hours'],
                'total_income': month_stats['total_income'],
                'total_expenses': month_stats['total_expenses'],
                'net_earnings': net_earnings,
                'after_tax_earnings': after_tax_earnings,
                'earnings_per_hour': earnings_per_hour,
                'after_tax_earnings_per_hour': after_tax_earnings_per_hour
            })

            month_start = month_start + relativedelta(months=1)

    # Sort monthly trends in reverse chronological order
    monthly_trends.sort(key=lambda x: x['month'], reverse=True)

    context = {
        'lesson_stats': overall_stats,
        'last_seven_days_stats': last_seven_days_stats,
        'current_week_stats': current_week_stats,
        'monthly_stats': monthly_stats,
        'weekly_trends': weekly_trends,
        'monthly_trends': monthly_trends,
        'quarterly_trends': quarterly_trends,
        'tax_ni_percentage': tax_ni_percentage
    }

    return render(request, 'JW/stats.html', context)


def health_check(request):
    """Health check endpoint for monitoring"""
    try:
        # Test database connection
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")

        # Test basic model access
        student_count = AppJwStudent.objects.count()

        return JsonResponse({
            'status': 'healthy',
            'database': 'connected',
            'students': student_count,
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)

@login_required
def manage_report_header(request):
    from .forms import UserSettingsForm
    from .models import AppJwUserSettings

    # Get or create report header fields for the user
    header_fields = {
        'name': '',
        'address': '',
        'phone': '',
        'email': '',
        'website': '',
        'business_number': '',
    }

    for field, default_value in header_fields.items():
        AppJwReportHeader.objects.get_or_create(
            user=request.user,
            field_name=field,
            defaults={'field_value': default_value, 'is_enabled': False}
        )

    # Get or create user settings
    user_settings, created = AppJwUserSettings.objects.get_or_create(
        user=request.user,
        defaults={'tax_ni_percentage': 21.00}
    )

    # Get test centres for the user
    test_centres = AppJwTestCentre.objects.filter(user=request.user, active=True).order_by('name')

    # Handle test centre form
    test_centre_form = TestCentreForm(user=request.user)

    if request.method == 'POST':
        # Handle test centre creation
        if 'add_test_centre' in request.POST:
            test_centre_form = TestCentreForm(request.POST, user=request.user)
            if test_centre_form.is_valid():
                test_centre = test_centre_form.save(commit=False)
                test_centre.user = request.user
                test_centre.save()
                messages.success(request, f'Test centre "{test_centre.name}" added successfully.')
                return redirect('manage_report_header')
            else:
                messages.error(request, 'Please correct the errors below.')

        # Handle test centre deletion
        elif 'delete_test_centre' in request.POST:
            test_centre_id = request.POST.get('test_centre_id')
            try:
                test_centre = AppJwTestCentre.objects.get(id=test_centre_id, user=request.user)
                test_centre.active = False
                test_centre.save()
                messages.success(request, f'Test centre "{test_centre.name}" deleted successfully.')
            except AppJwTestCentre.DoesNotExist:
                messages.error(request, 'Test centre not found.')
            return redirect('manage_report_header')

        # Handle report header fields
        else:
            for field in header_fields.keys():
                header = AppJwReportHeader.objects.get(user=request.user, field_name=field)
                header.field_value = request.POST.get(field, '')
                header.is_enabled = request.POST.get(f'{field}_enabled', '') == 'on'
                header.save()

            # Handle user settings form
            settings_form = UserSettingsForm(request.POST, instance=user_settings)
            if settings_form.is_valid():
                settings_form.save()
                messages.success(request, 'My Data updated successfully.')
            else:
                messages.error(request, 'Please correct the errors below.')

            return redirect('manage_report_header')

    # Get current values
    current_headers = {
        header.field_name: {
            'value': header.field_value,
            'enabled': header.is_enabled
        }
        for header in AppJwReportHeader.objects.filter(user=request.user)
    }

    settings_form = UserSettingsForm(instance=user_settings)

    context = {
        'headers': current_headers,
        'settings_form': settings_form,
        'test_centres': test_centres,
        'test_centre_form': test_centre_form,
    }

    return render(request, 'JW/report_header_form.html', context)

def get_report_header(user, format_html=False):
    """
    Get the enabled report header fields for a user.
    Args:
        user: The user object
        format_html: If True, returns formatted HTML string. If False, returns dictionary
    Returns:
        Either formatted header information as HTML or dictionary of header values
    """
    headers = AppJwReportHeader.objects.filter(
        user=user,
        is_enabled=True
    ).order_by('display_order')

    header_dict = {}
    for header in headers:
        if header.field_value:  # Only include non-empty values
            header_dict[header.field_name] = header.field_value

    if not format_html:
        return header_dict

    # Format the header information as HTML
    header_lines = []
    if 'name' in header_dict:
        header_lines.append(f"<h2>{header_dict['name']}</h2>")
    if 'address' in header_dict:
        header_lines.append(f"<p>{header_dict['address']}</p>")
    if 'phone' in header_dict:
        header_lines.append(f"<p>{header_dict['phone']}</p>")
    if 'email' in header_dict:
        header_lines.append(f"<p>{header_dict['email']}</p>")
    if 'website' in header_dict:
        header_lines.append(f"<p>{header_dict['website']}</p>")
    if 'business_number' in header_dict:
        header_lines.append(f"<p>Business Number: {header_dict['business_number']}</p>")

    return '\n'.join(header_lines)

# Keep this version of the function
def get_cached_stats(start_date, end_date):
    """Helper function to get cached statistics for a specific date range"""
    cache_key = f'stats_{start_date}_{end_date}'
    stats = cache.get(cache_key)

    if stats is None:
        # Get lesson statistics
        lesson_stats = AppJwLesson.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        ).aggregate(
            total_income=Sum(F('lesson_hours') * F('price_per_hour')),
            total_hours=Sum('lesson_hours'),
            lesson_count=Count('id')
        )

        # Get expense statistics
        expense_stats = AppJwBusinessexpense.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        ).aggregate(
            total_expenses=Sum('cost')
        )

        # Get fuel expense statistics
        fuel_stats = AppJwFuelexpense.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        ).aggregate(
            total_fuel=Sum('cost')
        )

        # Get mileage statistics
        mileage_stats = AppJwMileage.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        ).aggregate(
            total_miles=Sum('miles'),
            total_business_miles=Sum(Case(
                When(mileage_type='Business', then='miles'),
                default=0,
                output_field=DecimalField()
            )),
            total_personal_miles=Sum(Case(
                When(mileage_type='Personal', then='miles'),
                default=0,
                output_field=DecimalField()
            ))
        )

        stats = {
            'total_income': lesson_stats['total_income'] or Decimal('0.00'),
            'total_hours': lesson_stats['total_hours'] or Decimal('0.00'),
            'lesson_count': lesson_stats['lesson_count'] or 0,
            'total_expenses': (
                (expense_stats['total_expenses'] or Decimal('0.00')) +
                (fuel_stats['total_fuel'] or Decimal('0.00'))
            ),
            'total_miles': mileage_stats['total_miles'] or Decimal('0.00'),
            'business_miles': mileage_stats['total_business_miles'] or Decimal('0.00'),
            'personal_miles': mileage_stats['total_personal_miles'] or Decimal('0.00'),
        }

        # Cache for 1 hour
        cache.set(cache_key, stats, 3600)

    return stats

def get_gender_stats():
    """Helper function to get gender distribution statistics"""
    return AppJwStudent.objects.values('gender')\
        .annotate(count=Count('id'))\
        .order_by('gender')

def get_age_stats():
    """Helper function to get age distribution statistics"""
    ranges = [
        {'min': 17, 'max': 21, 'range': '17-20'},
        {'min': 21, 'max': 26, 'range': '21-25'},
        {'min': 26, 'max': 36, 'range': '26-35'},
        {'min': 36, 'max': 46, 'range': '36-45'},
        {'min': 46, 'max': 56, 'range': '46-55'},
        {'min': 56, 'max': None, 'range': '56+'},  # Changed this range
    ]

    stats = []
    for r in ranges:
        if r['max'] is None:  # Handle 56+ case
            count = AppJwStudent.objects.filter(age__gte=r['min']).count()
        else:
            count = AppJwStudent.objects.filter(age__gte=r['min'], age__lt=r['max']).count()
        if count > 0:
            stats.append({'range': r['range'], 'count': count})

    # Add count for blank/null ages
    blank_count = AppJwStudent.objects.filter(age__isnull=True).count()
    if blank_count > 0:
        stats.append({'range': 'blank', 'count': blank_count})

    return stats

def get_area_stats():
    """Helper function to get area distribution statistics"""
    stats = AppJwStudent.objects.values('area')\
        .annotate(count=Count('id'))\
        .exclude(area='')\
        .exclude(area__isnull=True)\
        .order_by('area')

    # Add count for blank/null areas
    blank_count = AppJwStudent.objects.filter(Q(area='') | Q(area__isnull=True)).count()
    if blank_count > 0:
        stats = list(stats)  # Convert to list to allow appending
        stats.append({'area': 'blank', 'count': blank_count})

    return stats

@login_required
def dashboard(request):
    """
    View for the main dashboard/landing page
    """
    return render(request, 'JW/dashboard.html')

def home(request):
    """
    Home page view - shows Google login or dashboard access
    """
    return render(request, 'JW/home.html')

def home(request):
    """
    Home page with Google login
    """
    if request.user.is_authenticated:
        return redirect('dashboard')
    return render(request, 'JW/home.html')

def unauthorized_access(request):
    """
    View to handle unauthorized Google login attempts
    """
    messages.error(
        request,
        'Unauthorized access. Your Google account is not authorized to access this system. '
        'Please contact the administrator if you believe this is an error.'
    )
    return redirect('home')

def google_auth_denied(request):
    """
    View to handle denied Google authentication
    """
    messages.error(
        request,
        'Google authentication failed. Please ensure you are using an authorized email address.'
    )
    return redirect('home')

