# New models to replace frontend logic with database tables
# This file shows what we could add to models.py to improve the architecture

from django.db import models
from django.contrib.auth.models import User
from decimal import Decimal


class TestResult(models.Model):
    """
    Dedicated table for test results instead of relying on student status + failed test records
    This replaces the complex frontend logic for determining test status
    """
    RESULT_CHOICES = [
        ('passed', 'Passed'),
        ('failed', 'Failed'),
    ]
    
    lesson = models.OneToOneField('AppJwLesson', on_delete=models.CASCADE, related_name='test_result')
    student = models.ForeignKey('AppJwStudent', on_delete=models.CASCADE, related_name='test_results')
    test_centre = models.ForeignKey('AppJwTestCentre', on_delete=models.SET_NULL, null=True, blank=True)
    result = models.CharField(max_length=10, choices=RESULT_CHOICES)
    test_date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'APP_JW_test_result'
        ordering = ['-test_date']
    
    def __str__(self):
        return f"{self.student.student_name} - {self.result} on {self.test_date}"


class AccountCreditTransaction(models.Model):
    """
    Track all credit transactions instead of calculating dynamically
    This replaces the complex JavaScript credit calculations
    """
    TRANSACTION_TYPES = [
        ('credit', 'Credit Added'),
        ('debit', 'Credit Used'),
        ('refund', 'Credit Refunded'),
        ('adjustment', 'Manual Adjustment'),
    ]
    
    account_credit = models.ForeignKey('AppJwBlockBooking', on_delete=models.CASCADE, related_name='transactions')
    lesson = models.ForeignKey('AppJwLesson', on_delete=models.CASCADE, null=True, blank=True, related_name='credit_transactions')
    transaction_type = models.CharField(max_length=15, choices=TRANSACTION_TYPES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    hours = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    
    class Meta:
        db_table = 'APP_JW_credit_transaction'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.transaction_type}: £{self.amount} ({self.account_credit.student_name})"


class StudentStatusHistory(models.Model):
    """
    Track student status changes instead of just storing current status
    This provides audit trail and better handling of status reversions
    """
    STATUS_CHOICES = [
        ('Yes', 'Active'),
        ('Passed', 'Passed Test'),
        ('No', 'Inactive'),
    ]
    
    student = models.ForeignKey('AppJwStudent', on_delete=models.CASCADE, related_name='status_history')
    old_status = models.CharField(max_length=10, choices=STATUS_CHOICES, null=True, blank=True)
    new_status = models.CharField(max_length=10, choices=STATUS_CHOICES)
    test_result = models.ForeignKey(TestResult, on_delete=models.SET_NULL, null=True, blank=True)
    reason = models.CharField(max_length=100, blank=True)  # e.g., "Test passed", "Lesson deleted", "Manual change"
    changed_at = models.DateTimeField(auto_now_add=True)
    changed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    
    class Meta:
        db_table = 'APP_JW_student_status_history'
        ordering = ['-changed_at']
    
    def __str__(self):
        return f"{self.student.student_name}: {self.old_status} → {self.new_status}"


class LessonCalculation(models.Model):
    """
    Store calculated lesson values instead of calculating in JavaScript
    This improves performance and data consistency
    """
    lesson = models.OneToOneField('AppJwLesson', on_delete=models.CASCADE, related_name='calculation')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    credit_used = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    cash_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    remaining_credit_after = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    calculation_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'APP_JW_lesson_calculation'
    
    def __str__(self):
        return f"Calculation for {self.lesson.student_name} on {self.lesson.date}"


# Model methods that would be added to existing models:

class AppJwStudentExtensions:
    """
    Methods that would be added to the existing AppJwStudent model
    """
    
    def get_current_status(self):
        """Get current status from status history"""
        latest = self.status_history.first()
        return latest.new_status if latest else 'Yes'
    
    def change_status(self, new_status, reason, user, test_result=None):
        """Change status with proper audit trail"""
        old_status = self.get_current_status()
        
        # Create status history record
        StudentStatusHistory.objects.create(
            student=self,
            old_status=old_status,
            new_status=new_status,
            test_result=test_result,
            reason=reason,
            changed_by=user
        )
        
        # Update current status
        self.active = new_status
        self.save()
    
    def get_total_credit_balance(self):
        """Calculate total credit balance from transactions"""
        from django.db.models import Sum
        
        credits = AccountCreditTransaction.objects.filter(
            account_credit__student_name=self.student_name,
            transaction_type='credit'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        debits = AccountCreditTransaction.objects.filter(
            account_credit__student_name=self.student_name,
            transaction_type__in=['debit', 'refund']
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        return credits - debits


class AppJwLessonExtensions:
    """
    Methods that would be added to the existing AppJwLesson model
    """
    
    def calculate_amounts(self):
        """Calculate and store lesson amounts"""
        total = self.lesson_hours * self.price_per_hour
        
        # Get or create calculation record
        calc, created = LessonCalculation.objects.get_or_create(
            lesson=self,
            defaults={
                'total_amount': total,
                'credit_used': Decimal('0'),
                'cash_amount': total,
                'remaining_credit_after': Decimal('0')
            }
        )
        
        if not created:
            calc.total_amount = total
            calc.save()
        
        return calc
    
    def record_test_result(self, result, test_centre, user):
        """Record test result with proper data structure"""
        # Create test result record
        test_result = TestResult.objects.create(
            lesson=self,
            student=self.get_student(),
            test_centre=test_centre,
            result=result,
            test_date=self.date
        )
        
        # Update student status
        if result == 'passed':
            self.get_student().change_status('Passed', 'Test passed', user, test_result)
        
        return test_result


# Benefits of this approach:
"""
1. **Performance**: No more JavaScript calculations on every page load
2. **Data Integrity**: Database constraints ensure data consistency
3. **Audit Trail**: Full history of all changes
4. **Simpler Frontend**: Templates just display stored values
5. **Better Testing**: Business logic in Python is easier to test
6. **Concurrent Safety**: Database transactions prevent race conditions
7. **Reporting**: Much easier to generate reports from structured data
"""

# Migration strategy:
"""
1. Create new tables alongside existing ones
2. Populate new tables with current data
3. Update views to use new tables
4. Gradually remove JavaScript calculations
5. Eventually remove old fields/logic
"""
