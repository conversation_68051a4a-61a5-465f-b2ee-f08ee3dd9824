# Django Environment Variables
# Copy this file to .env and update the values for your environment

# Security Settings
SECRET_KEY=iuhiudfh7yy74hjb3iu4guyig3487uibjkbkjsdb3487y43h3ik4kbnkshbjkfduh89yi343fjge
DEBUG=False

# Database Configuration
DB_NAME=JW
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=**********
DB_PORT=5432

# Production Settings
ALLOWED_HOSTS=learntodrivewithjohn.co.uk,www.learntodrivewithjohn.co.uk,localhost,127.0.0.1

# Google OAuth Configuration
GOOGLE_OAUTH2_CLIENT_ID=your_google_client_id_here
GOOGLE_OAUTH2_CLIENT_SECRET=your_google_client_secret_here
