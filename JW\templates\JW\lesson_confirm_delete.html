{% extends 'JW/base.html' %}

{% block title %}Delete Lesson{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">Delete Lesson</h2>
                <p class="card-text">Are you sure you want to delete the lesson for {{ lesson.student_name }} on {{ lesson.date }}?</p>
                <form method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                    <a href="{% url 'lesson_list' %}" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
