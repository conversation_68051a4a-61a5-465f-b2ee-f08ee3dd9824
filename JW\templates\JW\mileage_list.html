{% extends 'JW/base.html' %}

{% block title %}Mileage{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 1400px;">
    <!-- Page Header and Stats - Only visible on xl screens -->
    <h2 class="d-none d-xl-block mb-4">Mileage</h2>
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Total Miles</div>
                    <div class="h4">{{ total_miles|floatformat:1 }}</div>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Business Miles</div>
                    <div class="h4">{{ business_miles|floatformat:1 }}</div>
                    <small class="text-muted">({{ business_percentage|floatformat:1 }}%)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Personal Miles</div>
                    <div class="h4">{{ personal_miles|floatformat:1 }}</div>
                    <small class="text-muted">({{ personal_percentage|floatformat:1 }}%)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Miles/Lesson</div>
                    <div class="h4">{{ avg_miles_per_lesson|floatformat:1 }}</div>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Miles/Hour</div>
                    <div class="h4">{{ avg_miles_per_hour|floatformat:1 }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second row of average statistics -->
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Miles/Week</div>
                    <div class="h5">{{ avg_miles_per_week_alltime|floatformat:1 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Miles/Month</div>
                    <div class="h5">{{ avg_miles_per_month_alltime|floatformat:1 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Miles/Quarter</div>
                    <div class="h5">{{ avg_miles_per_quarter_alltime|floatformat:1 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Third row of current period statistics -->
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Week Miles</div>
                    <div class="h5">{{ current_week_business_miles|floatformat:1 }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Month Miles</div>
                    <div class="h5">{{ current_month_business_miles|floatformat:1 }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Quarter Miles</div>
                    <div class="h5">{{ current_quarter_business_miles|floatformat:1 }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{% url 'mileage_create' %}" class="btn btn-primary w-100 h-100 d-flex align-items-center justify-content-center py-2">
            <div class="text-center">
                <i class="fas fa-route fa-2x mb-2"></i>
                <div>Add New Mileage Record</div>
            </div>
        </a>
    </div>

    <!-- Wrap table in a div that respects container width -->
    <div style="min-width: auto; overflow-x: auto;">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Day</th>
                    <th>Type</th>
                    <th>Miles</th>
                    <th>Notes</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for entry in mileage_entries %}
                <tr>
                    <td>{{ entry.date|date:"M d, Y" }}</td>
                    <td>{{ entry.day }}</td>
                    <td>{{ entry.mileage_type }}</td>
                    <td>{{ entry.miles }}</td>
                    <td>{{ entry.notes|default:"-"|truncatechars:30 }}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'mileage_update' entry.id %}" class="btn btn-sm btn-warning">Edit</a>
                            <a href="{% url 'mileage_delete' entry.id %}" class="btn btn-sm btn-danger">Delete</a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
