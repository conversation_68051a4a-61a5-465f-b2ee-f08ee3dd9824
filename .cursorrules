# Cursor Rules for Django Driving School Management System

## Project Context
This is a Django-based driving school management system with the following key components:
- Student management
- Lesson scheduling and tracking
- Block booking system
- Financial tracking (expenses, mileage)
- Test result management
- Google OAuth authentication

## MCP Tool Usage Rules

[[calls]]
match = "when the user requests Django code examples, Django documentation, or Django-specific functionality"
tool = "context7"
library = "/django/django"

[[calls]]
match = "when the user requests PostgreSQL queries, database schema information, or database operations"
tool = "postgres"

[[calls]]
match = "when the user requests file operations, directory listings, or file content"
tool = "filesystem"

[[calls]]
match = "when the user requests git operations, commit history, or version control information"
tool = "git"

[[calls]]
match = "when the user requests Python library documentation or Python-specific code examples"
tool = "context7"

[[calls]]
match = "when the user requests web search for current information, latest solutions, or troubleshooting"
tool = "brave-search"

## Code Quality Guidelines

- Always use proper error handling with try-catch blocks
- Use Django's transaction.atomic() for database operations
- Apply appropriate decorators from JW.utils.error_handling
- Log important user actions using log_user_action()
- Use SELECT FOR UPDATE for race condition prevention
- Follow Django best practices for views, models, and forms

## Security Guidelines

- Never expose credentials in code
- Use environment variables for sensitive configuration
- Apply proper authentication decorators (@login_required)
- Validate all user inputs
- Use Django's built-in security features

## Database Guidelines

- Use select_related() and prefetch_related() for query optimization
- Apply database constraints and validations
- Use atomic transactions for multi-step operations
- Handle IntegrityError and ValidationError appropriately

## Block Booking System Guidelines

- Always use SELECT FOR UPDATE when modifying block bookings
- Handle overdraft scenarios according to user settings
- Log all block booking operations
- Maintain data consistency between lessons and usage records

## Testing Guidelines

- Write tests for critical functionality
- Test error handling scenarios
- Test concurrent operations (especially block bookings)
- Validate security measures

## Documentation Guidelines

- Add docstrings to all functions and classes
- Document complex business logic
- Keep README and documentation up to date
- Document API endpoints and their usage
