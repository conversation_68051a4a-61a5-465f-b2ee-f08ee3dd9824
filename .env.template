# Django Configuration
DEBUG=False
SECRET_KEY=your_secret_key_here

# Database Configuration
DB_NAME=JW
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=**********
DB_PORT=5432

# Google OAuth Configuration
GOOGLE_OAUTH2_CLIENT_ID=222967636914-2lu0ph1j1qvbuj1g0ooa9nmf7v3un1sm.apps.googleusercontent.com
GOOGLE_OAUTH2_CLIENT_SECRET=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

# Security Settings
ALLOWED_HOSTS=learntodrivewithjohn.co.uk,www.learntodrivewithjohn.co.uk,localhost,127.0.0.1,**********,0.0.0.0

# Deployment Settings
CONTAINER_PORT=8002
HOST_IP=**********

# Optional: Email Configuration (if you plan to add email features)
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your_app_password
# EMAIL_USE_TLS=True

# Optional: Redis Configuration (for future caching)
# REDIS_URL=redis://localhost:6379/0

# Optional: Sentry Configuration (for error tracking)
# SENTRY_DSN=your_sentry_dsn_here

# Logging Level
LOG_LEVEL=INFO
