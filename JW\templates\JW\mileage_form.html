{% extends 'JW/base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ action }} Mileage{% endblock %}

{% block content %}
<style>
    /* Hide Day field on screens smaller than xl */
    @media (max-width: 1199.98px) {
        #div_id_day {
            display: none;
        }
    }
</style>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <h2>{{ action }} Mileage</h2>
        <form method="post" id="mileageForm">
            {% csrf_token %}
            {% if return_url %}
            <input type="hidden" name="return_url" value="{{ return_url }}">
            {% endif %}
            {{ form|crispy }}
            <div class="mt-3">
                <div class="row g-2">
                    <!-- Primary action button -->
                    <div class="col-12 col-sm-6">
                        <button type="submit" name="action" value="save" class="btn btn-primary w-100">Save</button>
                    </div>
                    
                    <!-- Create & Add Another -->
                    <div class="col-12 col-sm-6">
                        <button type="submit" name="action" value="save_and_add" class="btn btn-success w-100">Add Another</button>
                    </div>
                </div>
                <!-- Cancel button -->
                <div class="row mt-2">
                    <div class="col-12">
                        {% if return_url %}
                        <a href="{{ return_url }}" class="btn btn-secondary w-100">Cancel</a>
                        {% else %}
                        <a href="{% url 'mileage_list' %}" class="btn btn-secondary w-100">Cancel</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascript %}
<script>
(function() {
    // Get form elements
    const dateInput = document.querySelector('#id_date');
    const dayInput = document.querySelector('#id_day');

    // Set default date to today if it's a new form
    if (dateInput && !dateInput.value) {
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        dateInput.value = `${yyyy}-${mm}-${dd}`;
        updateDay();
    }

    // Function to update day
    function updateDay() {
        if (!dateInput.value) return;
        const date = new Date(dateInput.value);
        if (isNaN(date.getTime())) return;
        
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        dayInput.value = days[date.getDay()];
    }

    // Add event listeners
    if (dateInput) {
        dateInput.addEventListener('input', updateDay);
        dateInput.addEventListener('change', updateDay);
        // Initialize day if date exists
        if (dateInput.value) {
            updateDay();
        }
    }
})();
</script>
{% endblock %}
