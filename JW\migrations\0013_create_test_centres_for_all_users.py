# Generated manually to create test centres for all users

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('JW', '0012_fix_test_centre_unique_constraint'),
    ]

    operations = [
        # Create test centres for all existing users
        migrations.RunSQL(
            """
            INSERT INTO "APP_JW_test_centre" (name, user_id, active, created_at)
            SELECT 'Ashford', u.id, TRUE, NOW()
            FROM auth_user u
            WHERE NOT EXISTS (
                SELECT 1 FROM "APP_JW_test_centre" tc
                WHERE tc.name = 'Ashford' AND tc.user_id = u.id
            );

            INSERT INTO "APP_JW_test_centre" (name, user_id, active, created_at)
            SELECT 'Canterbury', u.id, TRUE, NOW()
            FROM auth_user u
            WHERE NOT EXISTS (
                SELECT 1 FROM "APP_JW_test_centre" tc
                WHERE tc.name = 'Canterbury' AND tc.user_id = u.id
            );

            INSERT INTO "APP_JW_test_centre" (name, user_id, active, created_at)
            SELECT 'Folkestone', u.id, TRUE, NOW()
            FROM auth_user u
            WHERE NOT EXISTS (
                SELECT 1 FROM "APP_JW_test_centre" tc
                WHERE tc.name = 'Folkestone' AND tc.user_id = u.id
            );
            """,
            reverse_sql="""
            DELETE FROM "APP_JW_test_centre"
            WHERE name IN ('Ashford', 'Canterbury', 'Folkestone');
            """
        ),
    ]
