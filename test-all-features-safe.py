#!/usr/bin/env python3
"""
Comprehensive test of all block booking features using Test Student Features.
This is safe to run as it uses a dedicated test student.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from django.db import transaction
from JW.models import AppJwStudent, AppJwBlockBooking, AppJwLesson, AppJwBlockBookingUsage, AppJwUserSettings
from django.contrib.auth.models import User
from decimal import Decimal
from datetime import date, timedelta
from django.db.models import Sum

def cleanup_test_data(student):
    """Clean up all test data for the given student."""
    print(f"🗑️ Deleting test data...")

    # Delete in correct order (foreign key dependencies)
    usage_records = AppJwBlockBookingUsage.objects.filter(block_booking__student=student)
    usage_count = usage_records.count()
    usage_records.delete()
    print(f"✅ Deleted {usage_count} usage records")

    lessons = AppJwLesson.objects.filter(student=student)
    lesson_count = lessons.count()
    lessons.delete()
    print(f"✅ Deleted {lesson_count} lessons")

    bookings = AppJwBlockBooking.objects.filter(student=student)
    booking_count = bookings.count()
    bookings.delete()
    print(f"✅ Deleted {booking_count} block bookings")

    # Reset student settings
    student.block_booking_disabled = False
    student.save()
    print(f"✅ Reset student settings to defaults")

    # Clean up test user settings
    try:
        test_user = User.objects.get(username='test_safe_features')
        test_user_settings = AppJwUserSettings.objects.filter(user=test_user)
        if test_user_settings.exists():
            test_user_settings.delete()
            print(f"✅ Cleaned up test user settings")
        test_user.delete()
        print(f"✅ Deleted test user")
    except User.DoesNotExist:
        pass

    print(f"🎉 Cleanup Complete! Test Student Features is ready for future testing.")

def test_all_features_safe():
    """Comprehensive test using Test Student Features."""
    
    print("🧪 Comprehensive Block Booking Test - Using Test Student Features")
    print("=" * 70)
    
    try:
        # Setup test user and settings
        user, created = User.objects.get_or_create(
            username='test_safe_features',
            defaults={'email': '<EMAIL>'}
        )
        
        user_settings, created = AppJwUserSettings.objects.get_or_create(
            user=user,
            defaults={
                'tax_ni_percentage': Decimal('21.00'),
                'allow_block_booking_overdraft': False
            }
        )
        
        # Get or create Test Student Features
        try:
            student = AppJwStudent.objects.get(student_name='Test Student Features')
            print(f"📋 Using test student: {student.student_name} (ID: {student.id})")
        except AppJwStudent.DoesNotExist:
            print("❌ Test Student Features not found. Creating test student...")
            student = AppJwStudent.objects.create(
                student_name='Test Student Features',
                area='Test Area',
                active='Yes',
                block_booking_disabled=False,
                notes='Dedicated test student for safe feature testing'
            )
            print(f"✅ Created test student: {student.student_name} (ID: {student.id})")
        
        # Clean up any existing test data
        print(f"\n🧹 Cleaning existing test data...")
        AppJwBlockBookingUsage.objects.filter(block_booking__student=student).delete()
        AppJwLesson.objects.filter(student=student).delete()
        AppJwBlockBooking.objects.filter(student=student).delete()
        
        # Reset student settings
        student.block_booking_disabled = False
        student.save()
        
        print(f"✅ Clean slate ready")
        
        # Test 1: Basic Block Booking Creation and Usage
        print(f"\n📊 Test 1: Basic Block Booking Creation and Usage")
        
        booking1 = AppJwBlockBooking.objects.create(
            student=student,
            date_created=date.today() - timedelta(days=10),
            amount_paid=Decimal('200.00'),
            total_lessons=Decimal('10.0'),
            lessons_used=Decimal('0.0'),
            active=True,
            calculation_method='legacy',
            notes='Test booking 1 - Legacy method'
        )
        
        print(f"   Created legacy booking: £{booking1.amount_paid} for {booking1.total_lessons} lessons")
        print(f"   Available credit: {booking1.lessons_remaining} lessons")
        
        # Use some credit
        lesson1 = AppJwLesson.objects.create(
            day_of_week='Monday',
            date=date.today() - timedelta(days=9),
            lesson_hours=Decimal('2.0'),
            student_name=student.student_name,
            user=user,
            price_per_hour=Decimal('20.00'),
            student=student,
            notes='Test lesson 1 - Legacy booking'
        )
        
        success = booking1.add_usage(lesson1.lesson_hours, lesson1.price_per_hour)
        if success:
            AppJwBlockBookingUsage.objects.create(
                block_booking=booking1,
                lesson=lesson1,
                lessons_used=lesson1.lesson_hours,
                date_used=lesson1.date
            )
            booking1.refresh_from_db()
            print(f"   ✅ Used {lesson1.lesson_hours} hours, remaining: {booking1.lessons_remaining}")
        
        # Test 2: New Method with Remainder Balance
        print(f"\n💰 Test 2: New Method with Remainder Balance")
        
        booking2 = AppJwBlockBooking.objects.create(
            student=student,
            date_created=date.today() - timedelta(days=5),
            amount_paid=Decimal('250.00'),
            total_lessons=Decimal('10.0'),
            lessons_used=Decimal('0.0'),
            active=True,
            calculation_method='new',
            price_per_lesson_fixed=Decimal('22.00'),
            remainder_balance=Decimal('30.00'),  # £250 - (10 × £22) = £30
            notes='Test booking 2 - New method with remainder'
        )
        
        print(f"   Created new method booking: £{booking2.amount_paid}")
        print(f"   Lesson allocation: {booking2.total_lessons} × £{booking2.price_per_lesson_fixed}")
        print(f"   Remainder balance: £{booking2.remainder_balance}")
        print(f"   Total value: £{booking2.total_value_remaining}")
        
        # Test normal rate lesson (uses lesson allocation)
        lesson2 = AppJwLesson.objects.create(
            day_of_week='Tuesday',
            date=date.today() - timedelta(days=4),
            lesson_hours=Decimal('1.0'),
            student_name=student.student_name,
            user=user,
            price_per_hour=Decimal('22.00'),  # Matches fixed price
            student=student,
            notes='Test lesson 2 - Normal rate'
        )
        
        success = booking2.add_usage(lesson2.lesson_hours, lesson2.price_per_hour)
        if success:
            AppJwBlockBookingUsage.objects.create(
                block_booking=booking2,
                lesson=lesson2,
                lessons_used=lesson2.lesson_hours,
                date_used=lesson2.date
            )
            booking2.refresh_from_db()
            print(f"   ✅ Used lesson allocation: {lesson2.lesson_hours} lesson")
        
        # Test higher rate lesson (uses allocation + remainder)
        lesson3 = AppJwLesson.objects.create(
            day_of_week='Wednesday',
            date=date.today() - timedelta(days=3),
            lesson_hours=Decimal('2.0'),
            student_name=student.student_name,
            user=user,
            price_per_hour=Decimal('28.00'),  # £56 total vs £44 allocation = £12 from remainder
            student=student,
            notes='Test lesson 3 - Higher rate using remainder'
        )
        
        success = booking2.add_usage(lesson3.lesson_hours, lesson3.price_per_hour)
        if success:
            AppJwBlockBookingUsage.objects.create(
                block_booking=booking2,
                lesson=lesson3,
                lessons_used=lesson3.lesson_hours,
                date_used=lesson3.date
            )
            booking2.refresh_from_db()
            print(f"   ✅ Used allocation + remainder for higher rate lesson")
            print(f"   Remaining: {booking2.lessons_remaining} lessons, £{booking2.remainder_balance} remainder")
        
        # Test 3: Overdraft Control
        print(f"\n🚫 Test 3: Overdraft Control")
        
        # Test with overdraft disabled (default)
        lesson4 = AppJwLesson.objects.create(
            day_of_week='Thursday',
            date=date.today() - timedelta(days=2),
            lesson_hours=Decimal('15.0'),  # More than available
            student_name=student.student_name,
            user=user,
            price_per_hour=Decimal('25.00'),
            student=student,
            notes='Test lesson 4 - Should fail without overdraft'
        )
        
        success = booking2.add_usage(lesson4.lesson_hours, lesson4.price_per_hour)
        if not success:
            print(f"   ✅ Correctly rejected lesson exceeding credit (overdraft disabled)")
            lesson4.delete()
        else:
            print(f"   ❌ Should have rejected lesson exceeding credit")
        
        # Enable overdraft and test
        user_settings.allow_block_booking_overdraft = True
        user_settings.save()
        print(f"   Overdraft enabled for testing")
        
        # Test 4: Block Booking Disable Feature
        print(f"\n🔒 Test 4: Block Booking Disable Feature")
        
        # Try to disable with remaining balance (should fail validation)
        current_balance = booking2.total_value_remaining
        print(f"   Current balance: £{current_balance}")
        
        if current_balance > 0:
            print(f"   ❌ Cannot disable block booking with remaining balance")
            print(f"   (This would be caught by form validation)")
        
        # Use remaining balance first
        remaining_value = booking2.total_value_remaining
        if remaining_value > 0:
            exact_hours = remaining_value / Decimal('25.00')
            lesson5 = AppJwLesson.objects.create(
                day_of_week='Friday',
                date=date.today() - timedelta(days=1),
                lesson_hours=exact_hours,
                student_name=student.student_name,
                user=user,
                price_per_hour=Decimal('25.00'),
                student=student,
                notes='Test lesson 5 - Use remaining balance'
            )
            
            success = booking2.add_usage(lesson5.lesson_hours, lesson5.price_per_hour)
            if success:
                AppJwBlockBookingUsage.objects.create(
                    block_booking=booking2,
                    lesson=lesson5,
                    lessons_used=lesson5.lesson_hours,
                    date_used=lesson5.date
                )
                booking2.refresh_from_db()
                print(f"   ✅ Used remaining balance: £{remaining_value}")
                print(f"   Booking active: {booking2.active}")
        
        # Now can disable block booking
        if booking2.total_value_remaining <= 0:
            student.block_booking_disabled = True
            student.save()
            print(f"   ✅ Block booking disabled (zero balance)")
        
        # Test lesson recording with block booking disabled
        lesson6 = AppJwLesson.objects.create(
            day_of_week='Saturday',
            date=date.today(),
            lesson_hours=Decimal('1.0'),
            student_name=student.student_name,
            user=user,
            price_per_hour=Decimal('25.00'),
            student=student,
            notes='Test lesson 6 - Block booking disabled'
        )
        
        print(f"   ✅ Lesson recorded as pay-per-lesson (block booking disabled)")
        
        # Test 5: Re-enable Block Booking
        print(f"\n🔄 Test 5: Re-enable Block Booking")
        
        student.block_booking_disabled = False
        student.save()
        print(f"   ✅ Block booking re-enabled for future use")
        
        # Test 6: Remainder Carryover
        print(f"\n🔄 Test 6: Remainder Carryover Test")
        
        # Create new booking with remainder
        booking3 = AppJwBlockBooking.objects.create(
            student=student,
            date_created=date.today(),
            amount_paid=Decimal('180.00'),
            total_lessons=Decimal('8.0'),
            lessons_used=Decimal('0.0'),
            active=True,
            calculation_method='new',
            price_per_lesson_fixed=Decimal('20.00'),
            remainder_balance=Decimal('20.00'),  # £180 - (8 × £20) = £20
            notes='Test booking 3 - For carryover test'
        )
        
        print(f"   Created booking with remainder: £{booking3.remainder_balance}")
        
        # Simulate carryover to new booking
        carryover_amount = booking3.remainder_balance
        booking3.active = False
        booking3.remainder_balance = Decimal('0.00')
        booking3.save()
        
        booking4 = AppJwBlockBooking.objects.create(
            student=student,
            date_created=date.today(),
            amount_paid=Decimal('200.00') + carryover_amount,  # Include carryover
            total_lessons=Decimal('9.0'),
            lessons_used=Decimal('0.0'),
            active=True,
            calculation_method='new',
            price_per_lesson_fixed=Decimal('22.00'),
            notes='Test booking 4 - With carryover'
        )
        
        booking4.refresh_from_db()
        print(f"   ✅ Carryover successful: £{carryover_amount} carried over")
        print(f"   New booking total: £{booking4.amount_paid}")
        print(f"   New remainder: £{booking4.remainder_balance}")
        
        # Final Summary
        print(f"\n📊 Test Summary")
        print(f"=" * 70)
        
        all_bookings = AppJwBlockBooking.objects.filter(student=student).order_by('date_created')
        all_lessons = AppJwLesson.objects.filter(student=student).order_by('date')
        
        print(f"Block Bookings Created: {all_bookings.count()}")
        for i, booking in enumerate(all_bookings, 1):
            print(f"  {i}. £{booking.amount_paid} ({booking.calculation_method}) - Active: {booking.active}")
            print(f"     Lessons: {booking.lessons_used}/{booking.total_lessons}")
            if booking.calculation_method == 'new':
                print(f"     Remainder: £{booking.remainder_balance}")
        
        print(f"\nLessons Recorded: {all_lessons.count()}")
        for i, lesson in enumerate(all_lessons, 1):
            print(f"  {i}. {lesson.date}: {lesson.lesson_hours}h × £{lesson.price_per_hour} = £{lesson.lesson_hours * lesson.price_per_hour}")
        
        print(f"\n🎉 All Tests Completed Successfully!")
        print(f"✅ Legacy method block booking")
        print(f"✅ New method with remainder balance")
        print(f"✅ Mixed lesson rates (allocation + remainder)")
        print(f"✅ Overdraft control")
        print(f"✅ Block booking disable/enable")
        print(f"✅ Remainder carryover")
        print(f"✅ Pay-per-lesson when disabled")
        
        # Automatic cleanup after successful tests
        print(f"\n🧹 Cleaning up test data for Test Student Features...")
        cleanup_test_data(student)

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

        # Still clean up even if tests failed
        try:
            student = AppJwStudent.objects.get(student_name='Test Student Features')
            print(f"\n🧹 Cleaning up test data after failure...")
            cleanup_test_data(student)
        except:
            pass

if __name__ == "__main__":
    test_all_features_safe()
