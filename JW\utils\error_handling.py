"""
Error handling utilities for the JW application.
Provides centralized error handling, logging, and user-friendly error messages.
"""

import logging
import traceback
from functools import wraps
from typing import Any, Callable, Optional, Type, Union
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.exceptions import ValidationError
from django.db import IntegrityError, DatabaseError, OperationalError
from django.shortcuts import redirect
from django.urls import reverse

logger = logging.getLogger(__name__)

class JWError(Exception):
    """Base exception class for JW application errors."""
    def __init__(self, message: str, user_message: Optional[str] = None, error_code: Optional[str] = None):
        self.message = message
        self.user_message = user_message or message
        self.error_code = error_code
        super().__init__(self.message)

class DatabaseConnectionError(JWError):
    """Raised when database connection fails."""
    pass

class BlockBookingError(JWError):
    """Raised when block booking operations fail."""
    pass

class PDFGenerationError(JWError):
    """Raised when PDF generation fails."""
    pass

class AuthenticationError(JWError):
    """Raised when authentication fails."""
    pass

def handle_database_errors(func: Callable) -> Callable:
    """
    Decorator to handle database-related errors gracefully.
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        try:
            return func(request, *args, **kwargs)
        except OperationalError as e:
            logger.error(f"Database connection error in {func.__name__}: {str(e)}")
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': 'Database connection error. Please try again.',
                    'error_code': 'DB_CONNECTION_ERROR'
                }, status=503)
            else:
                messages.error(request, 'Database connection error. Please try again in a moment.')
                return redirect('dashboard')
        except DatabaseError as e:
            logger.error(f"Database error in {func.__name__}: {str(e)}")
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': 'Database error occurred. Please contact support if this persists.',
                    'error_code': 'DB_ERROR'
                }, status=500)
            else:
                messages.error(request, 'A database error occurred. Please contact support if this persists.')
                return redirect('dashboard')
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}\n{traceback.format_exc()}")
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': 'An unexpected error occurred. Please try again.',
                    'error_code': 'UNEXPECTED_ERROR'
                }, status=500)
            else:
                messages.error(request, 'An unexpected error occurred. Please try again.')
                return redirect('dashboard')
    return wrapper

def handle_form_errors(func: Callable) -> Callable:
    """
    Decorator to handle form validation and integrity errors.
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        try:
            return func(request, *args, **kwargs)
        except IntegrityError as e:
            error_message = str(e).lower()
            if 'unique' in error_message:
                if 'student_name' in error_message:
                    user_message = 'A student with this name already exists.'
                elif 'mobile_number' in error_message:
                    user_message = 'This mobile number is already registered.'
                else:
                    user_message = 'This information already exists in the database.'
            elif 'mobile_number_check' in error_message:
                user_message = 'Invalid mobile number format. Please enter a valid UK mobile number.'
            else:
                user_message = 'There was an error saving the data. Please check your information.'
            
            logger.warning(f"Integrity error in {func.__name__}: {str(e)}")
            
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': user_message,
                    'error_code': 'INTEGRITY_ERROR'
                }, status=400)
            else:
                messages.error(request, user_message)
                # Return to the same page to show the error
                return func(request, *args, **kwargs)
        except ValidationError as e:
            logger.warning(f"Validation error in {func.__name__}: {str(e)}")
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': str(e),
                    'error_code': 'VALIDATION_ERROR'
                }, status=400)
            else:
                messages.error(request, str(e))
                return func(request, *args, **kwargs)
    return wrapper

def safe_pdf_generation(func: Callable) -> Callable:
    """
    Decorator to handle PDF generation errors safely.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"PDF generation error in {func.__name__}: {str(e)}\n{traceback.format_exc()}")
            raise PDFGenerationError(
                f"Failed to generate PDF: {str(e)}",
                "Unable to generate PDF report. Please try again or contact support."
            )
    return wrapper

def log_user_action(action: str, user_id: Optional[int] = None, details: Optional[dict] = None):
    """
    Log user actions for audit trail.
    """
    log_data = {
        'action': action,
        'user_id': user_id,
        'details': details or {}
    }
    logger.info(f"User action: {log_data}")

def handle_block_booking_errors(func: Callable) -> Callable:
    """
    Decorator specifically for block booking operations.
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        try:
            return func(request, *args, **kwargs)
        except BlockBookingError as e:
            logger.warning(f"Block booking error in {func.__name__}: {str(e)}")
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': e.user_message,
                    'error_code': e.error_code or 'BLOCK_BOOKING_ERROR'
                }, status=400)
            else:
                messages.error(request, e.user_message)
                return redirect(request.META.get('HTTP_REFERER', 'dashboard'))
        except Exception as e:
            logger.error(f"Unexpected error in block booking {func.__name__}: {str(e)}\n{traceback.format_exc()}")
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': 'An error occurred with the block booking. Please try again.',
                    'error_code': 'BLOCK_BOOKING_UNEXPECTED_ERROR'
                }, status=500)
            else:
                messages.error(request, 'An error occurred with the block booking. Please try again.')
                return redirect(request.META.get('HTTP_REFERER', 'dashboard'))
    return wrapper

def get_user_friendly_error_message(error: Exception) -> str:
    """
    Convert technical errors to user-friendly messages.
    """
    error_str = str(error).lower()
    
    if isinstance(error, OperationalError):
        if 'connection' in error_str:
            return "Unable to connect to the database. Please try again in a moment."
        elif 'timeout' in error_str:
            return "The operation timed out. Please try again."
    elif isinstance(error, IntegrityError):
        if 'unique' in error_str:
            return "This information already exists in the system."
        elif 'foreign key' in error_str:
            return "Cannot complete this action due to related data constraints."
    elif isinstance(error, ValidationError):
        return str(error)
    
    return "An unexpected error occurred. Please try again or contact support."

def create_error_response(request, error: Exception, default_redirect: str = 'dashboard') -> Union[JsonResponse, HttpResponse]:
    """
    Create appropriate error response based on request type.
    """
    user_message = get_user_friendly_error_message(error)
    
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'error': user_message,
            'error_code': type(error).__name__
        }, status=500)
    else:
        messages.error(request, user_message)
        return redirect(default_redirect)
