-- Drop existing student table if it exists
DROP TABLE IF EXISTS "APP_JW_student";

-- Create the new student table
CREATE TABLE IF NOT EXISTS "APP_JW_student" (
    id SERIAL PRIMARY KEY,
    student_name VARCHAR(30) UNIQUE NOT NULL,
    mobile_number VARCHAR(20) CHECK (mobile_number ~ '^(\+44|0)[0-9]{10,11}$' OR mobile_number IS NULL),
    email_address VARCHAR(100) CHECK (email_address ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' OR email_address IS NULL),
    address_1st_line VARCHAR(100),
    address_2nd_line VARCHAR(100),
    area VARCHAR(50),
    post_code VARCHAR(10) CHECK (post_code ~ '^[A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}$' OR post_code IS NULL),
    gender VARCHAR(6) CHECK (gender IN ('Male', 'Female') OR gender IS NULL),
    age INTEGER CHECK (age > 0 OR age IS NULL),
    active VARCHAR(6) NOT NULL DEFAULT 'Yes' CHECK (active IN ('Yes', 'No', 'Passed')),
    notes TEXT,
    test_past DATE
);

-- Create indexes
CREATE INDEX idx_app_jw_student_name ON "APP_JW_student"(student_name);
CREATE INDEX idx_app_jw_student_email ON "APP_JW_student"(email_address);
CREATE INDEX idx_app_jw_student_active ON "APP_JW_student"(active);
CREATE INDEX idx_app_jw_student_post_code ON "APP_JW_student"(post_code);

-- Insert unique students from lessons table
INSERT INTO "APP_JW_student" (student_name, active)
SELECT DISTINCT student_name, 'Yes' as active
FROM "APP_JW_lesson"
ORDER BY student_name;

-- In your APP_JW_lesson table:
ALTER TABLE "APP_JW_lesson" 
    ADD COLUMN student_id INTEGER REFERENCES "APP_JW_student"(id);

-- Update existing records:
UPDATE "APP_JW_lesson" l
SET student_id = s.id
FROM "APP_JW_student" s
WHERE l.student_name = s.student_name;

-- Add index for better performance:
CREATE INDEX idx_app_jw_lesson_student_id ON "APP_JW_lesson"(student_id);
