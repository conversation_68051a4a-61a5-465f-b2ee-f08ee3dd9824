from django import template
from django.db.models import Q

register = template.Library()

@register.filter
def filter_by_gender(students, gender):
    return [s for s in students if s.gender == gender]

@register.filter
def filter_by_blank_gender(students):
    return [s for s in students if not s.gender or s.gender.strip() == '']

@register.filter
def filter_by_area(students, area):
    return [s for s in students if s.area == area]

@register.filter
def filter_by_blank_area(students):
    return [s for s in students if not s.area or s.area.strip() == '']

@register.filter
def split(value, delimiter=','):
    return value.split(delimiter)

@register.filter
def filter_by_age(queryset, age_range):
    def is_valid_age(age):
        if age is None:
            return False
        if isinstance(age, int):
            return True
        if isinstance(age, str):
            return age.strip().isdigit()
        return False

    def get_age_value(age):
        if isinstance(age, int):
            return age
        if isinstance(age, str) and age.strip().isdigit():
            return int(age.strip())
        return None

    if age_range == 'blank':
        return [s for s in queryset if not is_valid_age(s.age)]
    
    # Filter out invalid ages first
    valid_students = [s for s in queryset if is_valid_age(s.age)]
    
    if age_range == '56+':
        return [s for s in valid_students if get_age_value(s.age) >= 56]
    
    try:
        min_age, max_age = map(int, age_range.split('-'))
        return [s for s in valid_students if min_age <= get_age_value(s.age) <= max_age]
    except ValueError:
        return []

@register.filter
def filter_by_blank_age(queryset):
    def is_valid_age(age):
        if age is None:
            return False
        if isinstance(age, str) and not age.strip():
            return False
        return True
    
    return [s for s in queryset if not is_valid_age(s.age)]
