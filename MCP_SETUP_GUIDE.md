# MCP Services Setup Guide

This guide will help you set up Context7 MCP service and other useful MCP servers to enhance your development experience with AI assistants.

## 🎯 What is MCP?

Model Context Protocol (MCP) allows AI assistants to access external tools and data sources. This dramatically improves the quality and accuracy of AI responses by providing up-to-date, context-specific information.

## 🚀 Context7 MCP Server Setup

Context7 provides up-to-date documentation for libraries and frameworks, eliminating outdated or hallucinated API responses.

### For VS Code (Recommended)

1. **Install Context7 MCP Server:**
   ```bash
   # One-click install (if using VS Code Insiders)
   # Click this link: https://insiders.vscode.dev/redirect?url=vscode%3Amcp%2Finstall%3F%7B%22name%22%3A%22context7%22%2C%22command%22%3A%22npx%22%2C%22args%22%3A%5B%22-y%22%2C%22%40upstash%2Fcontext7-mcp%40latest%22%5D%7D
   ```

2. **Manual Configuration:**
   Add to your VS Code settings.json:
   ```json
   "mcp": {
     "servers": {
       "context7": {
         "type": "stdio",
         "command": "npx",
         "args": ["-y", "@upstash/context7-mcp"]
       }
     }
   }
   ```

### For Cursor

1. **One-click install:**
   Click: https://cursor.com/install-mcp?name=context7&config=eyJjb21tYW5kIjoibnB4IC15IEB1cHN0YXNoL2NvbnRleHQ3LW1jcCJ9

2. **Manual Configuration:**
   Add to `~/.cursor/mcp.json`:
   ```json
   {
     "mcpServers": {
       "context7": {
         "command": "npx",
         "args": ["-y", "@upstash/context7-mcp"]
       }
     }
   }
   ```

### For Claude Desktop

Add to `claude_desktop_config.json`:
```json
{
  "mcpServers": {
    "Context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp"]
    }
  }
}
```

## 🛠️ Other Useful MCP Servers

### 1. **Filesystem MCP** - File Operations
```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/your/project"]
    }
  }
}
```

### 2. **Git MCP** - Git Operations
```json
{
  "mcpServers": {
    "git": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-git"]
    }
  }
}
```

### 3. **Database MCP** - Database Queries
```json
{
  "mcpServers": {
    "postgres": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-postgres"],
      "env": {
        "POSTGRES_CONNECTION_STRING": "postgresql://user:password@localhost:5432/database"
      }
    }
  }
}
```

### 4. **Web Search MCP** - Real-time Web Search
```json
{
  "mcpServers": {
    "brave-search": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search"],
      "env": {
        "BRAVE_API_KEY": "your_brave_api_key"
      }
    }
  }
}
```

## 🎯 How to Use Context7

### Basic Usage
Add `use context7` to your prompts:
```
Create a Next.js middleware that checks for JWT tokens. use context7
```

### Advanced Usage with Library IDs
If you know the specific library:
```
Implement authentication with Supabase. use library /supabase/supabase for API docs
```

### Auto-invoke with Rules
Add to your `.windsurfrules` (Windsurf) or Cursor Rules:
```
[[calls]]
match = "when the user requests code examples, setup or configuration steps, or library/API documentation"
tool = "context7"
```

## 🔧 Troubleshooting

### Common Issues

1. **Module Not Found Errors:**
   Try using `bunx` instead of `npx`:
   ```json
   {
     "command": "bunx",
     "args": ["-y", "@upstash/context7-mcp"]
   }
   ```

2. **ESM Resolution Issues:**
   Add experimental VM modules flag:
   ```json
   {
     "command": "npx",
     "args": ["-y", "--node-options=--experimental-vm-modules", "@upstash/context7-mcp"]
   }
   ```

3. **TLS/Certificate Issues:**
   Use experimental fetch flag:
   ```json
   {
     "command": "npx",
     "args": ["-y", "--node-options=--experimental-fetch", "@upstash/context7-mcp"]
   }
   ```

## 🚀 Benefits for Your Django Project

With these MCP servers set up, you'll get:

1. **Up-to-date Django Documentation** - Context7 provides current Django docs
2. **Database Query Assistance** - PostgreSQL MCP can help with database operations
3. **File Operations** - Filesystem MCP for project file management
4. **Git Integration** - Git MCP for version control operations
5. **Real-time Information** - Web search for latest solutions

## 📝 Example Prompts

Once set up, try these prompts:

```
# Django-specific with Context7
Create a Django middleware for rate limiting. use context7

# Database operations
Show me the current schema for the APP_JW_student table

# File operations
List all Python files in the JW directory that contain "views"

# Git operations
Show me the recent commits related to block booking functionality
```

## 🎉 Next Steps

1. Install Context7 MCP server using the method for your editor
2. Test with a simple prompt: "Create a Django view with error handling. use context7"
3. Add other MCP servers based on your needs
4. Set up auto-invoke rules for seamless experience

This setup will significantly improve the quality and accuracy of AI assistance for your Django project!
