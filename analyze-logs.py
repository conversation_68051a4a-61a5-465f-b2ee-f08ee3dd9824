#!/usr/bin/env python3
"""
Log analysis script for the Django driving school management system.
Analyzes performance metrics, errors, and user activity from log files.
"""

import re
import json
import sys
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from pathlib import Path

class LogAnalyzer:
    def __init__(self, log_file_path):
        self.log_file_path = Path(log_file_path)
        self.performance_metrics = []
        self.errors = []
        self.user_activities = []
        self.slow_operations = []
        
    def parse_log_file(self):
        """Parse the log file and extract relevant information."""
        if not self.log_file_path.exists():
            print(f"❌ Log file not found: {self.log_file_path}")
            return False
        
        try:
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        self.parse_line(line.strip(), line_num)
                    except Exception as e:
                        print(f"⚠️ Error parsing line {line_num}: {e}")
            return True
        except Exception as e:
            print(f"❌ Error reading log file: {e}")
            return False
    
    def parse_line(self, line, line_num):
        """Parse a single log line."""
        # Extract timestamp
        timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
        timestamp = timestamp_match.group(1) if timestamp_match else None
        
        # Parse performance metrics
        if 'PERFORMANCE_METRIC:' in line:
            try:
                json_part = line.split('PERFORMANCE_METRIC: ')[1]
                metric_data = json.loads(json_part)
                metric_data['line_num'] = line_num
                metric_data['log_timestamp'] = timestamp
                self.performance_metrics.append(metric_data)
            except (json.JSONDecodeError, IndexError):
                pass
        
        # Parse errors
        elif 'ERROR' in line and ('ERROR_WITH_CONTEXT:' in line or 'BLOCK_BOOKING_ERROR:' in line):
            try:
                if 'ERROR_WITH_CONTEXT:' in line:
                    json_part = line.split('ERROR_WITH_CONTEXT: ')[1]
                    error_data = json.loads(json_part)
                else:
                    error_data = {
                        'error_type': 'BlockBookingError',
                        'error_message': line,
                        'timestamp': timestamp
                    }
                error_data['line_num'] = line_num
                error_data['log_timestamp'] = timestamp
                self.errors.append(error_data)
            except (json.JSONDecodeError, IndexError):
                pass
        
        # Parse user activities
        elif 'USER_ACTIVITY:' in line:
            try:
                json_part = line.split('USER_ACTIVITY: ')[1]
                activity_data = json.loads(json_part)
                activity_data['line_num'] = line_num
                activity_data['log_timestamp'] = timestamp
                self.user_activities.append(activity_data)
            except (json.JSONDecodeError, IndexError):
                pass
        
        # Parse slow operations
        elif 'SLOW_OPERATION:' in line:
            slow_op_match = re.search(r'SLOW_OPERATION: (.+) took ([\d.]+)s with (\d+) queries', line)
            if slow_op_match:
                self.slow_operations.append({
                    'operation': slow_op_match.group(1),
                    'duration': float(slow_op_match.group(2)),
                    'query_count': int(slow_op_match.group(3)),
                    'timestamp': timestamp,
                    'line_num': line_num
                })
    
    def analyze_performance(self):
        """Analyze performance metrics."""
        print("\n📊 Performance Analysis")
        print("=" * 30)
        
        if not self.performance_metrics:
            print("No performance metrics found in logs.")
            return
        
        # Group metrics by name
        metrics_by_name = defaultdict(list)
        for metric in self.performance_metrics:
            metrics_by_name[metric['metric']].append(metric['value'])
        
        # Calculate statistics for each metric
        for metric_name, values in metrics_by_name.items():
            if values:
                avg_value = sum(values) / len(values)
                max_value = max(values)
                min_value = min(values)
                
                print(f"\n{metric_name}:")
                print(f"  Count: {len(values)}")
                print(f"  Average: {avg_value:.3f}")
                print(f"  Min: {min_value:.3f}")
                print(f"  Max: {max_value:.3f}")
                
                # Highlight concerning metrics
                if 'execution_time' in metric_name and avg_value > 1.0:
                    print(f"  ⚠️ High average execution time!")
                elif 'query_count' in metric_name and avg_value > 10:
                    print(f"  ⚠️ High average query count!")
    
    def analyze_errors(self):
        """Analyze error patterns."""
        print("\n🚨 Error Analysis")
        print("=" * 20)
        
        if not self.errors:
            print("No errors found in logs.")
            return
        
        # Count errors by type
        error_types = Counter(error['error_type'] for error in self.errors)
        
        print(f"Total errors: {len(self.errors)}")
        print("\nError types:")
        for error_type, count in error_types.most_common():
            print(f"  {error_type}: {count}")
        
        # Show recent errors
        recent_errors = sorted(self.errors, key=lambda x: x.get('timestamp', ''), reverse=True)[:5]
        if recent_errors:
            print("\nRecent errors:")
            for error in recent_errors:
                print(f"  {error.get('timestamp', 'Unknown time')}: {error['error_type']} - {error['error_message'][:100]}...")
    
    def analyze_user_activity(self):
        """Analyze user activity patterns."""
        print("\n👥 User Activity Analysis")
        print("=" * 30)
        
        if not self.user_activities:
            print("No user activities found in logs.")
            return
        
        # Count activities by type
        activity_types = Counter(activity['activity_type'] for activity in self.user_activities)
        
        print(f"Total activities: {len(self.user_activities)}")
        print("\nActivity types:")
        for activity_type, count in activity_types.most_common():
            print(f"  {activity_type}: {count}")
        
        # Count activities by user
        user_activities = Counter(activity['user_id'] for activity in self.user_activities)
        print(f"\nMost active users:")
        for user_id, count in user_activities.most_common(5):
            print(f"  User {user_id}: {count} activities")
    
    def analyze_slow_operations(self):
        """Analyze slow operations."""
        print("\n🐌 Slow Operations Analysis")
        print("=" * 35)
        
        if not self.slow_operations:
            print("No slow operations found in logs.")
            return
        
        print(f"Total slow operations: {len(self.slow_operations)}")
        
        # Group by operation type
        ops_by_type = defaultdict(list)
        for op in self.slow_operations:
            ops_by_type[op['operation']].append(op)
        
        print("\nSlow operations by type:")
        for op_type, ops in ops_by_type.items():
            avg_duration = sum(op['duration'] for op in ops) / len(ops)
            avg_queries = sum(op['query_count'] for op in ops) / len(ops)
            print(f"  {op_type}: {len(ops)} occurrences, avg {avg_duration:.2f}s, avg {avg_queries:.1f} queries")
        
        # Show slowest operations
        slowest_ops = sorted(self.slow_operations, key=lambda x: x['duration'], reverse=True)[:5]
        print("\nSlowest operations:")
        for op in slowest_ops:
            print(f"  {op['operation']}: {op['duration']:.2f}s ({op['query_count']} queries) at {op['timestamp']}")
    
    def generate_report(self):
        """Generate a comprehensive analysis report."""
        print("🔍 Django Application Log Analysis Report")
        print("=" * 50)
        print(f"Log file: {self.log_file_path}")
        print(f"Analysis time: {datetime.now()}")
        
        if not self.parse_log_file():
            return
        
        self.analyze_performance()
        self.analyze_errors()
        self.analyze_user_activity()
        self.analyze_slow_operations()
        
        print("\n✅ Analysis complete!")

def main():
    if len(sys.argv) != 2:
        print("Usage: python analyze-logs.py <log_file_path>")
        print("Example: python analyze-logs.py django.log")
        sys.exit(1)
    
    log_file = sys.argv[1]
    analyzer = LogAnalyzer(log_file)
    analyzer.generate_report()

if __name__ == "__main__":
    main()
