# Generated by Django 4.2 on 2025-02-21 22:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('JW', '0005_appjwstudent'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppJwFailedTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_name', models.CharField(max_length=30)),
                ('test_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'APP_JW_failed_test',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppJwReportHeader',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=50)),
                ('field_value', models.CharField(max_length=255)),
                ('is_enabled', models.BooleanField(default=False)),
                ('display_order', models.IntegerField(default=0)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'APP_JW_report_header',
                'unique_together': {('user', 'field_name')},
            },
        ),
    ]
