from django.core.management.base import BaseCommand
from django.db import connection
import os

class Command(BaseCommand):
    help = 'Updates the student active field to support Passed status'

    def handle(self, *args, **options):
        # Read the SQL file
        sql_file_path = os.path.join('JW', 'sql', 'update_student_active_field.sql')
        with open(sql_file_path, 'r') as sql_file:
            sql = sql_file.read()

        # Execute the SQL
        with connection.cursor() as cursor:
            cursor.execute(sql)

        self.stdout.write(self.style.SUCCESS('Successfully updated student active field'))