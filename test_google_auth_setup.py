#!/usr/bin/env python
"""
Test script to verify Google OAuth setup for the Driving School Management System
"""

import os
import sys
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from django.contrib.auth.models import User
from django.contrib.sites.models import Site
from allauth.socialaccount.models import SocialApp

def test_setup():
    """Test the Google OAuth setup"""
    print("🔍 Testing Google OAuth Setup...")
    print("=" * 50)
    
    # Test 1: Check if john user exists
    try:
        john_user = User.objects.get(username='john')
        print("✅ Target user 'john' exists")
        print(f"   - ID: {john_user.id}")
        print(f"   - Email: {john_user.email}")
        print(f"   - Is staff: {john_user.is_staff}")
        print(f"   - Is superuser: {john_user.is_superuser}")
    except User.DoesNotExist:
        print("❌ Target user 'john' not found")
        print("   Run: python manage.py setup_google_auth --create-john-user")
        return False
    
    # Test 2: Check Site configuration
    try:
        site = Site.objects.get(id=1)
        print("✅ Site configuration exists")
        print(f"   - Domain: {site.domain}")
        print(f"   - Name: {site.name}")
    except Site.DoesNotExist:
        print("❌ Site configuration not found")
        print("   Run: python manage.py setup_google_auth")
        return False
    
    # Test 3: Check Google OAuth app
    try:
        google_app = SocialApp.objects.get(provider='google')
        print("✅ Google OAuth app configured")
        print(f"   - Name: {google_app.name}")
        print(f"   - Client ID: {google_app.client_id[:10]}...")
        print(f"   - Has secret: {'Yes' if google_app.secret else 'No'}")
        print(f"   - Sites: {list(google_app.sites.values_list('domain', flat=True))}")
    except SocialApp.DoesNotExist:
        print("❌ Google OAuth app not configured")
        print("   Run: python manage.py setup_google_auth --client-id YOUR_ID --client-secret YOUR_SECRET")
        return False
    
    # Test 4: Check environment variables
    client_id = os.getenv('GOOGLE_OAUTH2_CLIENT_ID')
    client_secret = os.getenv('GOOGLE_OAUTH2_CLIENT_SECRET')
    
    if client_id and client_secret:
        print("✅ Environment variables configured")
        print(f"   - GOOGLE_OAUTH2_CLIENT_ID: {client_id[:10]}...")
        print(f"   - GOOGLE_OAUTH2_CLIENT_SECRET: {'*' * len(client_secret)}")
    else:
        print("⚠️  Environment variables not set")
        print("   Add GOOGLE_OAUTH2_CLIENT_ID and GOOGLE_OAUTH2_CLIENT_SECRET to .env")
    
    # Test 5: Check settings
    print("✅ Settings configuration")
    print(f"   - Allowed emails: {settings.ALLOWED_GOOGLE_EMAILS}")
    print(f"   - Target username: {settings.GOOGLE_AUTH_TARGET_USERNAME}")
    print(f"   - Login URL: {settings.LOGIN_URL}")
    print(f"   - Login redirect: {settings.LOGIN_REDIRECT_URL}")
    
    # Test 6: Check installed apps
    required_apps = [
        'django.contrib.sites',
        'allauth',
        'allauth.account',
        'allauth.socialaccount',
        'allauth.socialaccount.providers.google',
    ]
    
    missing_apps = [app for app in required_apps if app not in settings.INSTALLED_APPS]
    if missing_apps:
        print(f"❌ Missing apps: {missing_apps}")
        return False
    else:
        print("✅ All required apps installed")
    
    print("\n" + "=" * 50)
    print("🎉 Google OAuth setup verification complete!")
    print("\nNext steps:")
    print("1. Ensure Google Cloud Console redirect URI is set to:")
    print(f"   https://{site.domain}/accounts/google/login/callback/")
    print("2. Test login at: /accounts/login/")
    print("3. Try logging in with authorized emails:")
    for email in settings.ALLOWED_GOOGLE_EMAILS:
        print(f"   - {email}")
    
    return True

if __name__ == '__main__':
    try:
        success = test_setup()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Error during setup verification: {e}")
        sys.exit(1)