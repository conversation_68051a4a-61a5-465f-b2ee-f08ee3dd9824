#!/usr/bin/env python3
"""
Test script to validate all the improvements made to the Django application.
This script tests error handling, database operations, and logging functionality.
"""

import os
import sys
import django
import threading
import time
from decimal import Decimal
from datetime import date, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from django.db import transaction, IntegrityError
from django.contrib.auth.models import User
from django.test import RequestFactory
from django.contrib.messages.storage.fallback import FallbackStorage
from django.contrib.sessions.middleware import SessionMiddleware
from JW.models import AppJwStudent, AppJwBlockBooking, AppJwLesson, AppJwBlockBookingUsage
from JW.utils.error_handling import log_user_action, JWError, BlockBookingError
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestSuite:
    def __init__(self):
        self.factory = RequestFactory()
        self.test_results = []
        
    def log_test_result(self, test_name, passed, message=""):
        result = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append((test_name, passed, message))
        print(f"{result}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_error_handling_utilities(self):
        """Test the error handling utilities"""
        print("\n🔧 Testing Error Handling Utilities...")
        
        try:
            # Test custom exception
            raise BlockBookingError("Test error", "User friendly message", "TEST_ERROR")
        except BlockBookingError as e:
            self.log_test_result(
                "Custom Exception Creation",
                e.user_message == "User friendly message" and e.error_code == "TEST_ERROR"
            )
        
        # Test user action logging
        try:
            log_user_action("test_action", 1, {"test": "data"})
            self.log_test_result("User Action Logging", True)
        except Exception as e:
            self.log_test_result("User Action Logging", False, str(e))
    
    def test_database_connection_pooling(self):
        """Test database connection pooling configuration"""
        print("\n🗄️ Testing Database Connection Pooling...")
        
        from django.conf import settings
        db_config = settings.DATABASES['default']
        
        has_conn_max_age = 'CONN_MAX_AGE' in db_config
        has_health_checks = db_config.get('CONN_HEALTH_CHECKS', False)
        
        self.log_test_result(
            "Connection Pooling Configuration",
            has_conn_max_age and has_health_checks,
            f"CONN_MAX_AGE: {db_config.get('CONN_MAX_AGE')}, Health Checks: {has_health_checks}"
        )
    
    def test_block_booking_race_conditions(self):
        """Test race condition prevention in block booking operations"""
        print("\n🏁 Testing Block Booking Race Condition Prevention...")
        
        # Create test user and student
        try:
            user, created = User.objects.get_or_create(username='test_user')
            student, created = AppJwStudent.objects.get_or_create(
                student_name='Test Student Race Condition',
                defaults={'active': 'Yes'}
            )
            
            # Create a block booking
            block_booking = AppJwBlockBooking.objects.create(
                student=student,
                date_created=date.today(),
                amount_paid=Decimal('100.00'),
                total_lessons=Decimal('10.0'),
                lessons_used=Decimal('0.0'),
                active=True,
                calculation_method='legacy'
            )
            
            # Test concurrent access simulation
            def add_usage_worker(booking_id, hours, price):
                try:
                    booking = AppJwBlockBooking.objects.get(id=booking_id)
                    return booking.add_usage(hours, price)
                except Exception as e:
                    logger.error(f"Worker error: {e}")
                    return False
            
            # Simulate concurrent operations
            results = []
            threads = []
            
            for i in range(3):
                thread = threading.Thread(
                    target=lambda: results.append(
                        add_usage_worker(block_booking.id, Decimal('2.0'), Decimal('25.00'))
                    )
                )
                threads.append(thread)
                thread.start()
            
            for thread in threads:
                thread.join()
            
            # Check results
            successful_operations = sum(results)
            block_booking.refresh_from_db()
            
            self.log_test_result(
                "Race Condition Prevention",
                successful_operations <= 5,  # Should not exceed available lessons
                f"Successful operations: {successful_operations}, Final usage: {block_booking.lessons_used}"
            )
            
            # Cleanup
            block_booking.delete()
            student.delete()
            
        except Exception as e:
            self.log_test_result("Race Condition Prevention", False, str(e))
    
    def test_logging_configuration(self):
        """Test logging configuration"""
        print("\n📝 Testing Logging Configuration...")
        
        from django.conf import settings
        logging_config = settings.LOGGING
        
        # Check for required handlers
        handlers = logging_config.get('handlers', {})
        required_handlers = ['file', 'security_file', 'error_file']
        
        has_all_handlers = all(handler in handlers for handler in required_handlers)
        
        # Check for rotating file handler
        file_handler = handlers.get('file', {})
        is_rotating = file_handler.get('class') == 'logging.handlers.RotatingFileHandler'
        
        self.log_test_result(
            "Logging Configuration",
            has_all_handlers and is_rotating,
            f"Handlers present: {list(handlers.keys())}"
        )
    
    def test_security_configuration(self):
        """Test security configuration"""
        print("\n🔒 Testing Security Configuration...")
        
        from django.conf import settings
        
        # Check security settings
        security_checks = {
            'SESSION_COOKIE_HTTPONLY': getattr(settings, 'SESSION_COOKIE_HTTPONLY', False),
            'SESSION_COOKIE_SAMESITE': getattr(settings, 'SESSION_COOKIE_SAMESITE', None) == 'Lax',
            'SECURE_CONTENT_TYPE_NOSNIFF': getattr(settings, 'SECURE_CONTENT_TYPE_NOSNIFF', False),
            'SECURE_BROWSER_XSS_FILTER': getattr(settings, 'SECURE_BROWSER_XSS_FILTER', False),
        }
        
        passed_checks = sum(security_checks.values())
        total_checks = len(security_checks)
        
        self.log_test_result(
            "Security Configuration",
            passed_checks == total_checks,
            f"Passed {passed_checks}/{total_checks} security checks"
        )
    
    def test_environment_variables(self):
        """Test environment variable configuration"""
        print("\n🌍 Testing Environment Variables...")
        
        from django.conf import settings
        import os
        
        # Check if key environment variables are being used
        env_checks = {
            'DEBUG': hasattr(settings, 'DEBUG'),
            'SECRET_KEY': bool(settings.SECRET_KEY),
            'DB_NAME': 'DB_NAME' in os.environ or settings.DATABASES['default']['NAME'],
            'GOOGLE_OAUTH2_CLIENT_ID': bool(getattr(settings, 'GOOGLE_OAUTH2_CLIENT_ID', None)),
        }
        
        passed_checks = sum(env_checks.values())
        total_checks = len(env_checks)
        
        self.log_test_result(
            "Environment Variables",
            passed_checks == total_checks,
            f"Configured {passed_checks}/{total_checks} environment variables"
        )
    
    def run_all_tests(self):
        """Run all tests"""
        print("🧪 Starting Comprehensive Test Suite")
        print("=" * 50)
        
        self.test_error_handling_utilities()
        self.test_database_connection_pooling()
        self.test_block_booking_race_conditions()
        self.test_logging_configuration()
        self.test_security_configuration()
        self.test_environment_variables()
        
        # Summary
        print("\n📊 Test Results Summary")
        print("=" * 30)
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 All tests passed! Your improvements are working correctly.")
        else:
            print("\n⚠️ Some tests failed. Please review the issues above.")
            
        return passed == total

if __name__ == "__main__":
    test_suite = TestSuite()
    success = test_suite.run_all_tests()
    sys.exit(0 if success else 1)
