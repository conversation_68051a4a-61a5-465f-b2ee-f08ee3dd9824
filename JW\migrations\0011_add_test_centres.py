# Generated manually for test centre functionality

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('JW', '0010_add_block_booking_enhancements'),
    ]

    operations = [
        # Create test centre table
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS "APP_JW_test_centre" (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) UNIQUE NOT NULL,
                user_id INTEGER NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                active BOOLEAN DEFAULT TRUE
            );
            """,
            reverse_sql='DROP TABLE IF EXISTS "APP_JW_test_centre";'
        ),
        
        # Add test_centre column to student table
        migrations.RunSQL(
            """
            ALTER TABLE "APP_JW_student" 
            ADD COLUMN IF NOT EXISTS test_centre_id INTEGER 
            REFERENCES "APP_JW_test_centre"(id) ON DELETE SET NULL;
            """,
            reverse_sql='ALTER TABLE "APP_JW_student" DROP COLUMN IF EXISTS test_centre_id;'
        ),
        
        # Add test_centre column to failed_test table
        migrations.RunSQL(
            """
            ALTER TABLE "APP_JW_failed_test" 
            ADD COLUMN IF NOT EXISTS test_centre_id INTEGER 
            REFERENCES "APP_JW_test_centre"(id) ON DELETE SET NULL;
            """,
            reverse_sql='ALTER TABLE "APP_JW_failed_test" DROP COLUMN IF EXISTS test_centre_id;'
        ),
        
        # Insert default test centres
        migrations.RunSQL(
            """
            INSERT INTO "APP_JW_test_centre" (name, user_id, active) 
            SELECT 'Ashford', 1, TRUE
            WHERE NOT EXISTS (SELECT 1 FROM "APP_JW_test_centre" WHERE name = 'Ashford');
            
            INSERT INTO "APP_JW_test_centre" (name, user_id, active) 
            SELECT 'Canterbury', 1, TRUE
            WHERE NOT EXISTS (SELECT 1 FROM "APP_JW_test_centre" WHERE name = 'Canterbury');
            
            INSERT INTO "APP_JW_test_centre" (name, user_id, active) 
            SELECT 'Folkestone', 1, TRUE
            WHERE NOT EXISTS (SELECT 1 FROM "APP_JW_test_centre" WHERE name = 'Folkestone');
            """,
            reverse_sql='DELETE FROM "APP_JW_test_centre" WHERE name IN (\'Ashford\', \'Canterbury\', \'Folkestone\');'
        ),
    ]
