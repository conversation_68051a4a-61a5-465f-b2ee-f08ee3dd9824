{% extends 'JW/base.html' %}

{% block title %}Reports{% endblock %}

{% block content %}
<style>
    /* Custom color classes with appropriate text colors */
    .bg-purple {
        background-color: #6f42c1 !important;
        color: white !important;
    }
    .bg-teal {
        background-color: #20c997 !important;
        color: black !important;
    }
    .bg-indigo {
        background-color: #4b0082 !important;
        color: white !important;
    }
    .bg-pink {
        background-color: #e83e8c !important;
        color: white !important;
    }
    
    /* Override Bootstrap background colors with appropriate text colors */
    .bg-primary {
        color: white !important;
    }
    .bg-success {
        color: white !important;
    }
    .bg-danger {
        color: white !important;
    }
    .bg-info {
        color: black !important;
    }
    .bg-secondary {
        color: white !important;
    }
    .bg-dark {
        color: white !important;
    }
    .bg-warning {
        color: black !important;
    }
</style>

<div class="container">
    <h2 class="mb-4">Financial Reports</h2>
    
    <!-- Tax Year Reports Section -->
    <div class="card mb-5">
        <div class="card-header bg-danger">
            <h5 class="card-title mb-0">Tax Year Reports</h5>
        </div>
        <div class="card-body">
            <div class="row row-cols-1 row-cols-md-3 g-4">
                {% for year, data in tax_years.items %}
                    <div class="col">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">Tax Year {{ year }}/{{ year|add:"1"|stringformat:"02d"|slice:"-2:" }}</h5>
                                <div class="d-grid gap-2">
                                    <div class="btn-group">
                                        <a href="{% url 'tax_year_report' %}?start_year={{ year }}" 
                                           class="btn btn-primary">
                                            View Full Report
                                        </a>
                                        <a href="{% url 'tax_year_report_summary' %}?start_year={{ year }}" 
                                           class="btn btn-info">
                                            View Summary
                                        </a>
                                    </div>
                                    <div class="btn-group">
                                        <a href="{% url 'tax_year_report_pdf' %}?start_year={{ year }}" 
                                           class="btn btn-success">
                                            <i class="fas fa-download"></i> Full PDF
                                        </a>
                                        <a href="{% url 'tax_year_report_summary_pdf' %}?start_year={{ year }}" 
                                           class="btn btn-warning">
                                            <i class="fas fa-download"></i> Summary PDF
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="col">
                        <div class="alert alert-info">
                            No tax year reports available yet.
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Monthly Reports Section -->
    <div class="card mb-5">
        <div class="card-header bg-danger">
            <h5 class="card-title mb-0">Monthly Reports By Year</h5>
        </div>
        <div class="card-body">
            {% for year, data in reports_by_year.items %}
            <div class="card mb-4">
                <div class="card-header bg-{{ data.color }}">
                    <h5 class="card-title mb-0">{{ year }}</h5>
                </div>
                <div class="card-body">
                    <div class="row row-cols-1 row-cols-md-3 g-4">
                        {% for month in data.months %}
                        <div class="col">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">{{ month.month_name }}</h5>
                                    <div class="d-grid gap-2">
                                        <div class="btn-group">
                                            <a href="{% url 'monthly_report' %}?month={{ month.month_num }}&year={{ month.year }}" 
                                               class="btn btn-primary">
                                                View Full Report
                                            </a>
                                            <a href="{% url 'monthly_report_summary' %}?month={{ month.month_num }}&year={{ month.year }}" 
                                               class="btn btn-info">
                                                View Summary
                                            </a>
                                        </div>
                                        <div class="btn-group">
                                            <a href="{% url 'monthly_report_pdf' %}?month={{ month.month_num }}&year={{ month.year }}" 
                                               class="btn btn-success">
                                                <i class="fas fa-download"></i> Full PDF
                                            </a>
                                            <a href="{% url 'monthly_report_summary_pdf' %}?month={{ month.month_num }}&year={{ month.year }}" 
                                               class="btn btn-warning">
                                                <i class="fas fa-download"></i> Summary PDF
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="alert alert-info">
                No monthly reports available yet. Reports will appear here once you have entered some data.
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
