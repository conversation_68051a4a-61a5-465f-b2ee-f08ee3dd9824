# Generated manually for lesson page performance optimization

from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('JW', '0014_add_performance_indexes'),
    ]

    operations = [
        migrations.RunSQL(
            sql='''
            -- Additional indexes for lesson page performance
            CREATE INDEX IF NOT EXISTS idx_lesson_student_name ON "APP_JW_lesson"(student_name);
            CREATE INDEX IF NOT EXISTS idx_lesson_date_desc ON "APP_JW_lesson"(date DESC);
            CREATE INDEX IF NOT EXISTS idx_lesson_student_date ON "APP_JW_lesson"(student_id, date DESC);
            
            -- Indexes for failed test lookups
            CREATE INDEX IF NOT EXISTS idx_failed_test_lesson ON "APP_JW_failed_test"(lesson_id);
            
            -- Indexes for student test status lookups
            CREATE INDEX IF NOT EXISTS idx_student_active_test_past ON "APP_JW_student"(active, test_past);
            CREATE INDEX IF NOT EXISTS idx_student_name_active ON "APP_JW_student"(student_name, active);
            
            -- Block booking usage indexes for lesson page
            CREATE INDEX IF NOT EXISTS idx_block_booking_usage_lesson ON "APP_JW_block_booking_usage"(lesson_id);
            CREATE INDEX IF NOT EXISTS idx_block_booking_student_active ON "APP_JW_block_booking"(student_id, active);
            ''',
            reverse_sql='''
            -- Remove the indexes (for rollback)
            DROP INDEX IF EXISTS idx_lesson_student_name;
            DROP INDEX IF EXISTS idx_lesson_date_desc;
            DROP INDEX IF EXISTS idx_lesson_student_date;
            DROP INDEX IF EXISTS idx_failed_test_lesson;
            DROP INDEX IF EXISTS idx_student_active_test_past;
            DROP INDEX IF EXISTS idx_student_name_active;
            DROP INDEX IF EXISTS idx_block_booking_usage_lesson;
            DROP INDEX IF EXISTS idx_block_booking_student_active;
            '''
        ),
    ]
