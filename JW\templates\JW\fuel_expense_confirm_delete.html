{% extends 'JW/base.html' %}

{% block title %}Delete Fuel Expense{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">Delete Fuel Expense</h2>
                <p>Are you sure you want to delete this fuel expense?</p>
                
                <div class="mb-3">
                    <strong>Date:</strong> {{ object.date|date:"M d, Y" }}<br>
                    <strong>Day:</strong> {{ object.day_of_week }}<br>
                    <strong>Cost:</strong> £{{ object.cost|floatformat:2 }}<br>
                    {% if object.notes %}
                        <strong>Notes:</strong> {{ object.notes|linebreaks }}
                    {% endif %}
                </div>

                <form method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Confirm Delete</button>
                    <a href="{% url 'fuel_expense_list' %}" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
