from django.core.management.base import BaseCommand
from JW.models import AppJw<PERSON>esson, AppJwBusinessexpense, AppJwBusinessmileage, AppJwPersonalmileage
import calendar
from datetime import datetime

class Command(BaseCommand):
    help = 'Updates the day field for all records based on their date'

    def handle(self, *args, **options):
        # Function to get day name from date
        def get_day_name(date):
            return calendar.day_name[date.weekday()]

        # Update Lessons
        lessons = AppJwLesson.objects.all()
        lesson_count = 0
        for lesson in lessons:
            if lesson.date:
                lesson.day = get_day_name(lesson.date)
                lesson.save()
                lesson_count += 1
        self.stdout.write(self.style.SUCCESS(f'Updated {lesson_count} lessons'))

        # Update Business Expenses
        expenses = AppJwBusinessexpense.objects.all()
        expense_count = 0
        for expense in expenses:
            if expense.date:
                expense.day = get_day_name(expense.date)
                expense.save()
                expense_count += 1
        self.stdout.write(self.style.SUCCESS(f'Updated {expense_count} business expenses'))

        # Update Business Mileage
        business_mileage = AppJwBusinessmileage.objects.all()
        business_mileage_count = 0
        for mileage in business_mileage:
            if mileage.date:
                mileage.day = get_day_name(mileage.date)
                mileage.save()
                business_mileage_count += 1
        self.stdout.write(self.style.SUCCESS(f'Updated {business_mileage_count} business mileage records'))

        # Update Personal Mileage
        personal_mileage = AppJwPersonalmileage.objects.all()
        personal_mileage_count = 0
        for mileage in personal_mileage:
            if mileage.date:
                mileage.day = get_day_name(mileage.date)
                mileage.save()
                personal_mileage_count += 1
        self.stdout.write(self.style.SUCCESS(f'Updated {personal_mileage_count} personal mileage records'))

        total = lesson_count + expense_count + business_mileage_count + personal_mileage_count
        self.stdout.write(self.style.SUCCESS(f'Successfully updated {total} records in total'))
