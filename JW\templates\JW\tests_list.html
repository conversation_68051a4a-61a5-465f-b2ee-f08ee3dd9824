{% extends 'JW/base.html' %}

{% block title %}Tests{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 1400px;">
    <h2 class="mb-4">Test Results</h2>

    <!-- Test Centre Statistics -->
    {% if test_centre_stats %}
    <div class="row mb-4">
        {% for centre_name, stats in test_centre_stats.items %}
        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <h6 class="card-title mb-3">
                        <i class="fas fa-map-marker-alt"></i> {{ centre_name }}
                    </h6>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-success">
                                <i class="fas fa-check-circle"></i>
                                <div class="h4 mb-0">{{ stats.passed_count }}</div>
                                <small>Passed</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-danger">
                                <i class="fas fa-times-circle"></i>
                                <div class="h4 mb-0">{{ stats.failed_count }}</div>
                                <small>Failed</small>
                            </div>
                        </div>
                    </div>
                    <hr class="my-2">
                    <small class="text-muted">Total: {{ stats.total_count }} test{{ stats.total_count|pluralize }}</small>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Passed Tests Section -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-check-circle"></i> Passed Tests ({{ passed_tests|length }})
            </h5>
        </div>
        <div class="card-body">
            {% if passed_tests %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th style="width: 25%">Student Name</th>
                                <th style="width: 20%">Test Date</th>
                                <th style="width: 25%">Test Centre</th>
                                <th style="width: 15%">Outcome</th>
                                <th style="width: 15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in passed_tests %}
                            <tr>
                                <td>
                                    <a href="{% url 'student_detail' student.pk %}" class="text-decoration-none">
                                        {{ student.student_name }}
                                    </a>
                                </td>
                                <td>{{ student.test_past|date:"M d, Y" }}</td>
                                <td>
                                    {% if student.test_centre %}
                                        {{ student.test_centre.name }}
                                    {% else %}
                                        <span class="text-muted">Not specified</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-success">Passed</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'test_edit' student.pk %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i> Edit Details
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="showDeleteModal('passed', '{{ student.student_name }}', '{{ student.pk }}')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-muted">No passed tests recorded yet.</p>
            {% endif %}
        </div>
    </div>

    <!-- Failed Tests Section -->
    <div class="card">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">
                <i class="fas fa-times-circle"></i> Failed Tests ({{ failed_tests|length }})
            </h5>
        </div>
        <div class="card-body">
            {% if failed_tests %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th style="width: 25%">Student Name</th>
                                <th style="width: 20%">Test Date</th>
                                <th style="width: 25%">Test Centre</th>
                                <th style="width: 15%">Outcome</th>
                                <th style="width: 15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for failed_test in failed_tests %}
                            <tr>
                                <td>
                                    <a href="{% url 'student_lessons' student_name=failed_test.student_name %}" class="text-decoration-none">
                                        {{ failed_test.student_name }}
                                    </a>
                                </td>
                                <td>{{ failed_test.test_date|date:"M d, Y" }}</td>
                                <td>
                                    {% if failed_test.test_centre %}
                                        {{ failed_test.test_centre.name }}
                                    {% else %}
                                        <span class="text-muted">Not specified</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-danger">Failed</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'test_edit' failed_test.pk %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i> Edit Details
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="showDeleteModal('failed', '{{ failed_test.student_name }}', '{{ failed_test.pk }}')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-muted">No failed tests recorded yet.</p>
            {% endif %}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Delete Test Record</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning" role="alert">
                        <h6 class="alert-heading">⚠️ Important Notice</h6>
                        <p class="mb-0">To remove this test record, you need to delete the corresponding lesson.
                        This will automatically remove the test record from this page.</p>
                    </div>
                    <p>Student: <strong id="deleteStudentName"></strong></p>
                    <p>Test Type: <strong id="deleteTestType"></strong></p>
                    <p>Would you like to go to the lessons page to delete the corresponding lesson?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <a href="#" id="goToLessonsBtn" class="btn btn-primary">Go to Lessons</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showDeleteModal(testType, studentName, recordId) {
    document.getElementById('deleteStudentName').textContent = studentName;
    document.getElementById('deleteTestType').textContent = testType === 'passed' ? 'Passed Test' : 'Failed Test';

    // Set the link to go to student lessons page
    const lessonsUrl = "{% url 'student_lessons' student_name='PLACEHOLDER' %}".replace('PLACEHOLDER', encodeURIComponent(studentName));
    document.getElementById('goToLessonsBtn').href = lessonsUrl;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>

{% endblock %}
